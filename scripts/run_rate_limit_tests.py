#!/usr/bin/env python3
"""
Rate Limiting Test Runner

Runs all rate limiting tests and provides a summary.
"""

import os
import sys
import subprocess
import time

# Add the project root to the Python path
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

def run_test(test_file):
    """Run a single test file and return the result."""
    print(f"\n{'='*60}")
    print(f"Running {test_file}")
    print('='*60)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            [sys.executable, test_file],
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ PASSED ({duration:.2f}s)")
            # Extract test count from output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Ran' in line and 'tests' in line:
                    print(f"   {line.strip()}")
                    break
            return True, duration, result.stdout
        else:
            print(f"❌ FAILED ({duration:.2f}s)")
            print("STDOUT:")
            print(result.stdout)
            print("STDERR:")
            print(result.stderr)
            return False, duration, result.stdout + result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT (60s)")
        return False, 60, "Test timed out"
    except Exception as e:
        print(f"💥 ERROR: {e}")
        return False, 0, str(e)

def main():
    """Run all rate limiting tests."""
    print("🚀 SecuriNest Rate Limiting Test Suite")
    print("="*60)
    
    test_files = [
        "src/tests/test_rate_limit_config.py",
        "src/tests/test_rate_limit_service.py", 
        "src/tests/test_rate_limit_middleware.py",
        "src/tests/test_rate_limit_integration.py"
    ]
    
    results = []
    total_duration = 0
    
    for test_file in test_files:
        if not os.path.exists(os.path.join(project_root, test_file)):
            print(f"⚠️  Test file not found: {test_file}")
            continue
            
        passed, duration, output = run_test(test_file)
        results.append((test_file, passed, duration, output))
        total_duration += duration
    
    # Print summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print('='*60)
    
    passed_count = sum(1 for _, passed, _, _ in results if passed)
    total_count = len(results)
    
    for test_file, passed, duration, _ in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{status:10} {test_file:40} ({duration:.2f}s)")
    
    print(f"\n📈 Results: {passed_count}/{total_count} tests passed")
    print(f"⏱️  Total time: {total_duration:.2f}s")
    
    if passed_count == total_count:
        print("\n🎉 All rate limiting tests passed!")
        return 0
    else:
        print(f"\n💥 {total_count - passed_count} test(s) failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
