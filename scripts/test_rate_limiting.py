#!/usr/bin/env python3
"""
Rate Limiting Test Script

Simple script to test rate limiting functionality against a running SecuriNest instance.
"""

import asyncio
import aiohttp
import time
import sys
from typing import Dict, List, Tuple


class RateLimitTester:
    """Test rate limiting functionality."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, endpoint: str, method: str = "GET", max_requests: int = 20) -> Dict:
        """
        Test rate limiting on a specific endpoint.
        
        Args:
            endpoint: The endpoint to test (e.g., '/auth/login')
            method: HTTP method to use
            max_requests: Maximum number of requests to make
            
        Returns:
            Dictionary with test results
        """
        print(f"\n🧪 Testing {method} {endpoint}")
        print(f"Making up to {max_requests} requests...")
        
        results = {
            'endpoint': endpoint,
            'method': method,
            'requests': [],
            'rate_limited': False,
            'first_rate_limit_at': None,
            'retry_after': None
        }
        
        for i in range(max_requests):
            start_time = time.time()
            
            try:
                if method.upper() == "POST":
                    response = await self.session.post(f"{self.base_url}{endpoint}")
                else:
                    response = await self.session.get(f"{self.base_url}{endpoint}")
                
                duration = time.time() - start_time
                
                request_result = {
                    'request_number': i + 1,
                    'status_code': response.status,
                    'duration': duration,
                    'headers': dict(response.headers)
                }
                
                # Check for rate limiting
                if response.status == 429:
                    results['rate_limited'] = True
                    if results['first_rate_limit_at'] is None:
                        results['first_rate_limit_at'] = i + 1
                    
                    # Get retry-after header
                    retry_after = response.headers.get('Retry-After')
                    if retry_after:
                        results['retry_after'] = int(retry_after)
                    
                    # Get response body for rate limit details
                    try:
                        body = await response.json()
                        request_result['body'] = body
                    except:
                        request_result['body'] = await response.text()
                    
                    print(f"  Request {i+1}: ❌ Rate limited (429) - Retry after {retry_after}s")
                    
                    # Stop testing after rate limit is hit
                    results['requests'].append(request_result)
                    break
                
                else:
                    # Extract rate limit headers
                    rate_limit_headers = {}
                    for header in ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']:
                        if header in response.headers:
                            rate_limit_headers[header] = response.headers[header]
                    
                    if rate_limit_headers:
                        request_result['rate_limit_headers'] = rate_limit_headers
                        remaining = rate_limit_headers.get('X-RateLimit-Remaining', 'unknown')
                        print(f"  Request {i+1}: ✅ Success ({response.status}) - Remaining: {remaining}")
                    else:
                        print(f"  Request {i+1}: ✅ Success ({response.status}) - No rate limit headers")
                
                results['requests'].append(request_result)
                
                # Small delay between requests
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"  Request {i+1}: ❌ Error: {e}")
                results['requests'].append({
                    'request_number': i + 1,
                    'error': str(e)
                })
        
        return results
    
    async def test_critical_endpoints(self) -> List[Dict]:
        """Test all critical endpoints for rate limiting."""
        critical_endpoints = [
            ('/auth/login', 'POST'),
            ('/tenant', 'POST'),
            ('/tenant/verify-email', 'GET'),
        ]
        
        results = []
        
        for endpoint, method in critical_endpoints:
            result = await self.test_endpoint(endpoint, method)
            results.append(result)
            
            # Wait a bit between endpoint tests
            await asyncio.sleep(1)
        
        return results
    
    async def test_non_critical_endpoint(self) -> Dict:
        """Test a non-critical endpoint to verify it's not rate limited."""
        print("\n🔍 Testing non-critical endpoint (should not be rate limited)")
        return await self.test_endpoint('/docs', 'GET', max_requests=10)
    
    def print_summary(self, results: List[Dict]):
        """Print a summary of test results."""
        print("\n" + "="*60)
        print("📊 RATE LIMITING TEST SUMMARY")
        print("="*60)
        
        for result in results:
            endpoint = result['endpoint']
            method = result['method']
            rate_limited = result['rate_limited']
            first_limit = result['first_rate_limit_at']
            retry_after = result['retry_after']
            
            print(f"\n{method} {endpoint}:")
            print(f"  Rate Limited: {'✅ Yes' if rate_limited else '❌ No'}")
            
            if rate_limited:
                print(f"  First rate limit at request: {first_limit}")
                if retry_after:
                    print(f"  Retry after: {retry_after} seconds")
            
            # Show rate limit progression
            if result['requests']:
                print(f"  Request progression:")
                for req in result['requests'][:5]:  # Show first 5 requests
                    req_num = req['request_number']
                    status = req['status_code']
                    
                    if 'rate_limit_headers' in req:
                        remaining = req['rate_limit_headers'].get('X-RateLimit-Remaining', '?')
                        print(f"    {req_num}: {status} (Remaining: {remaining})")
                    else:
                        print(f"    {req_num}: {status}")


async def main():
    """Main test function."""
    print("🚀 SecuriNest Rate Limiting Test")
    print("="*40)

    # Check if help requested
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        print("Usage: python scripts/test_rate_limiting.py [BASE_URL]")
        print("")
        print("Arguments:")
        print("  BASE_URL    Base URL of the SecuriNest API (default: http://localhost:8000)")
        print("")
        print("Examples:")
        print("  python scripts/test_rate_limiting.py")
        print("  python scripts/test_rate_limiting.py http://localhost:8000")
        print("  python scripts/test_rate_limiting.py https://api.securinest.com")
        return 0

    # Check if custom URL provided
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    print(f"Testing against: {base_url}")
    
    async with RateLimitTester(base_url) as tester:
        try:
            # Test critical endpoints
            critical_results = await tester.test_critical_endpoints()
            
            # Test non-critical endpoint
            non_critical_result = await tester.test_non_critical_endpoint()
            
            # Print summary
            all_results = critical_results + [non_critical_result]
            tester.print_summary(all_results)
            
            # Check if rate limiting is working
            rate_limited_endpoints = [r for r in critical_results if r['rate_limited']]
            
            if rate_limited_endpoints:
                print(f"\n✅ Rate limiting is working! {len(rate_limited_endpoints)} critical endpoints are protected.")
            else:
                print(f"\n⚠️  Rate limiting may not be working. No critical endpoints were rate limited.")
                print("   This could mean:")
                print("   - Rate limiting is disabled")
                print("   - Rate limits are set too high")
                print("   - There's an issue with the rate limiting implementation")
            
            # Check non-critical endpoint
            if not non_critical_result['rate_limited']:
                print("✅ Non-critical endpoints are not rate limited (as expected).")
            else:
                print("⚠️  Non-critical endpoint was rate limited (unexpected).")
        
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
