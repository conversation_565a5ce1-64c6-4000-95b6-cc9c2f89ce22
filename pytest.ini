[tool:pytest]
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
asyncio_default_test_loop_scope = function
testpaths = src/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Add project root to Python path
pythonpath = .

# Default options
addopts = -v --tb=short --strict-markers --disable-warnings

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PytestDeprecationWarning
    ignore::pytest_asyncio.plugin.PytestDeprecationWarning

# Markers for test categorization
markers =
    rate_limiting: Rate limiting functionality tests
    security: Security-related tests
    integration: Integration tests
    unit: Unit tests
    slow: Slow running tests
