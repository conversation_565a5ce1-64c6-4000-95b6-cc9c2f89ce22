# SecuriNest Environment Configuration Example
# Copy this file to .env and update with your actual values

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://username:password@localhost:5432/securinest_db
AUTO_SEED_DATABASE=true
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_ECHO=false

# =============================================================================
# AUTHENTICATION & JWT CONFIGURATION
# =============================================================================
# JWT Settings
JWT_ALGORITHM=RS256
JWT_AUDIENCE=securinest
JWT_ISSUER_BASE_URL=http://localhost:8080/realms
JWT_CACHE_TTL=3600
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_HOURS=24

# Auth Secrets (Generate strong random values for production)
AUTH_STATE_SECRET=your_32_character_state_secret_here_for_oauth2_security
AUTH_NONCE_SECRET=your_32_character_nonce_secret_here_for_oauth2_security

# Cookie Configuration
AUTH_SESSION_COOKIE_NAME=session_token
AUTH_REFRESH_COOKIE_NAME=refresh_token
AUTH_COOKIE_DOMAIN=localhost
AUTH_COOKIE_SECURE=false
AUTH_COOKIE_SAMESITE=lax
AUTH_COOKIE_MAX_AGE_SECONDS=86400

# =============================================================================
# CSRF PROTECTION CONFIGURATION
# =============================================================================
# CSRF Secret Key (MUST be at least 32 characters, 64+ recommended for production)
CSRF_SECRET_KEY=your_64_character_csrf_secret_key_for_production_security_here

# CSRF Settings
CSRF_ENABLED=true
CSRF_TOKEN_HEADER=X-CSRF-Token
CSRF_COOKIE_NAME=csrf_token
CSRF_TOKEN_EXPIRE_MINUTES=60

# Exempt paths (comma-separated, no spaces)
CSRF_EXEMPT_PATHS=/auth/login,/auth/callback,/auth/logout,/docs,/openapi.json,/redoc

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Enable/disable CORS protection
CORS_ENABLED=true

# Allowed origins (comma-separated, no spaces)
# In production, specify your frontend domain(s) explicitly
# Example: CORS_ALLOW_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com
CORS_ALLOW_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080

# Allowed HTTP methods (comma-separated, no spaces)
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS

# Allowed headers (comma-separated, no spaces)
CORS_ALLOW_HEADERS=Accept,Accept-Language,Content-Language,Content-Type,Authorization,X-CSRF-Token,X-Requested-With

# Allow credentials (cookies, authorization headers)
CORS_ALLOW_CREDENTIALS=true

# Headers to expose to the client (comma-separated, no spaces)
CORS_EXPOSE_HEADERS=X-Total-Count,X-Rate-Limit-Remaining,X-Rate-Limit-Reset

# Max age for preflight requests in seconds (24 hours = 86400)
CORS_MAX_AGE=86400

# =============================================================================
# KEYCLOAK CONFIGURATION
# =============================================================================
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_CLIENT_SECRET=your_keycloak_client_secret
KEYCLOAK_MASTER_REALM=master
BACKEND_URL=http://localhost:8000

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_VERIFICATION_SECRET=your_32_character_email_verification_secret_here
EMAIL_SESSION_SECRET=your_32_character_email_session_secret_here
EMAIL_TOKEN_EXPIRY_HOURS=24

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=detailed
ENABLE_FILE_LOGGING=false
LOG_FILE_PATH=logs/securinest.log
MAX_LOG_FILE_SIZE_MB=10
LOG_FILE_BACKUP_COUNT=5

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Enable/disable rate limiting
RATE_LIMIT_ENABLED=true

# Redis configuration for rate limiting
# For local development: redis://localhost:6379
# For external Redis server with SSL: rediss://redis.securinest.com:6380
RATE_LIMIT_REDIS_URL=rediss://redis.securinest.com:6380
RATE_LIMIT_REDIS_PASSWORD=your_redis_password_here
RATE_LIMIT_REDIS_DB=0
RATE_LIMIT_REDIS_MAX_CONNECTIONS=10
RATE_LIMIT_REDIS_SSL=true
RATE_LIMIT_REDIS_SSL_CERT_REQS=none

# Fallback to in-memory rate limiting if Redis unavailable
RATE_LIMIT_FALLBACK_TO_MEMORY=true

# Rate limits (requests per minute per IP)
RATE_LIMIT_AUTH_LOGIN=10
RATE_LIMIT_AUTH_CALLBACK=20
RATE_LIMIT_AUTH_REFRESH=30
RATE_LIMIT_TENANT_CREATE=5
RATE_LIMIT_EMAIL_VERIFY=10
RATE_LIMIT_DEFAULT=60

# Rate limit window in seconds (60 = 1 minute)
RATE_LIMIT_WINDOW_SECONDS=60

# Memory fallback configuration
RATE_LIMIT_MEMORY_MAX_SIZE=10000
RATE_LIMIT_MEMORY_CLEANUP_INTERVAL=300

# Component-specific log levels
SQLALCHEMY_LOG_LEVEL=WARNING
SQLALCHEMY_POOL_LOG_LEVEL=WARNING
SQLALCHEMY_DIALECTS_LOG_LEVEL=WARNING
UVICORN_ACCESS_LOG_LEVEL=INFO
UVICORN_ERROR_LOG_LEVEL=ERROR
HTTPX_LOG_LEVEL=WARNING
HTTPCORE_LOG_LEVEL=WARNING
APP_SERVICE_LOG_LEVEL=INFO
CORE_SERVICE_LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=INFO
DATABASE_LOG_LEVEL=INFO

# =============================================================================
# PRODUCTION SECURITY NOTES
# =============================================================================
# For production deployment, ensure:
# 1. ENVIRONMENT=production
# 2. AUTH_COOKIE_SECURE=true (requires HTTPS)
# 3. AUTH_COOKIE_SAMESITE=strict (or lax)
# 4. Strong secrets (64+ characters)
# 5. Proper AUTH_COOKIE_DOMAIN setting
# 6. CSRF_ENABLED=true
# 7. Strong CSRF_SECRET_KEY (64+ characters)
# 8. Appropriate CSRF_TOKEN_EXPIRE_MINUTES (≤60 recommended)

# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================
# For development:
# - AUTH_COOKIE_SECURE=false (allows HTTP)
# - AUTH_COOKIE_SAMESITE=lax (more permissive)
# - CSRF_ENABLED=true (test CSRF protection)
# - AUTO_SEED_DATABASE=true (populate test data)
# - DB_ECHO=true (SQL query logging, if needed)
