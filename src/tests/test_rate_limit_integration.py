"""
Integration tests for rate limiting functionality.

Tests the complete rate limiting flow with real middleware and service integration.
"""

import asyncio
import os
import sys
import time
import unittest
from unittest.mock import patch

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.app.core.config.rate_limit_config import rate_limit_config
from src.app.core.service.rate_limit_service import rate_limit_service


class TestRateLimitIntegration(unittest.TestCase):
    """Integration tests for rate limiting."""

    def setUp(self):
        """Set up test fixtures."""
        pass

    def test_memory_backend_integration(self):
        """Test memory backend integration with service."""
        async def run_test():
            # Test that the service can use memory backend
            with patch.object(rate_limit_config, 'enabled', True):
                with patch.object(rate_limit_config, 'fallback_to_memory', True):
                    # Force use of memory backend by making Red<PERSON> fail
                    with patch.object(rate_limit_service._redis_backend, 'check_rate_limit', side_effect=Exception("<PERSON><PERSON> failed")):

                        # Should fall back to memory backend
                        allowed, count, retry_after = await rate_limit_service.check_rate_limit("test_key", "/auth/login")

                        self.assertTrue(allowed)
                        self.assertGreater(count, 0)
                        self.assertEqual(retry_after, 0)

        asyncio.run(run_test())

    def test_endpoint_specific_limits(self):
        """Test that different endpoints have different rate limits."""
        async def run_test():
            with patch.object(rate_limit_config, 'enabled', True):
                # Test that different endpoints get different limits
                login_limit = rate_limit_config.get_endpoint_limit('/auth/login')
                tenant_limit = rate_limit_config.get_endpoint_limit('/tenant')
                default_limit = rate_limit_config.get_endpoint_limit('/api/other')

                # Verify they are different (based on default config)
                self.assertNotEqual(login_limit, tenant_limit)
                self.assertNotEqual(login_limit, default_limit)

        asyncio.run(run_test())


if __name__ == '__main__':
    unittest.main()
