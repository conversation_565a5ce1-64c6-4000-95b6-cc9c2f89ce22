"""
Unit tests for rate limiting middleware.

Tests middleware integration, IP extraction, and response handling.
"""

import os
import sys
import unittest
from unittest.mock import AsyncMock, patch, MagicMock

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.app.core.middleware.rate_limit_middleware import RateLimitMiddleware
from src.app.core.config.rate_limit_config import rate_limit_config


class TestRateLimitMiddleware(unittest.TestCase):
    """Test rate limiting middleware."""

    def test_client_ip_extraction_direct(self):
        """Test client IP extraction from direct connection."""
        middleware = RateLimitMiddleware(None)

        # Mock request with direct client
        request = MagicMock()
        request.headers = {}
        request.client.host = "*************"

        ip = middleware._get_client_ip(request)
        self.assertEqual(ip, "*************")

    def test_client_ip_extraction_forwarded(self):
        """Test client IP extraction from X-Forwarded-For header."""
        middleware = RateLimitMiddleware(None)

        # Mock request with X-Forwarded-For header
        request = MagicMock()
        request.headers = {"X-Forwarded-For": "***********, ***********, ********"}
        request.client.host = "********"

        ip = middleware._get_client_ip(request)
        self.assertEqual(ip, "***********")  # Should get the first IP (client IP)

    def test_client_ip_extraction_no_client(self):
        """Test client IP extraction when client is None."""
        middleware = RateLimitMiddleware(None)

        # Mock request with no client
        request = MagicMock()
        request.headers = {}
        request.client = None

        ip = middleware._get_client_ip(request)
        self.assertEqual(ip, "unknown")

    def test_path_normalization(self):
        """Test path normalization for rate limiting."""
        middleware = RateLimitMiddleware(None)

        # Test specific paths that should remain unchanged
        self.assertEqual(middleware._get_normalized_path("/auth/login"), "/auth/login")
        self.assertEqual(middleware._get_normalized_path("/auth/callback"), "/auth/callback")
        self.assertEqual(middleware._get_normalized_path("/tenant"), "/tenant")
        self.assertEqual(middleware._get_normalized_path("/tenant/verify-email"), "/tenant/verify-email")

        # Test paths that should be normalized
        self.assertEqual(middleware._get_normalized_path("/tenant/123"), "/tenant/{id}")
        self.assertEqual(middleware._get_normalized_path("/auth/unknown"), "/auth/{endpoint}")

        # Test other paths remain unchanged
        self.assertEqual(middleware._get_normalized_path("/api/data"), "/api/data")

    def test_rate_limit_config_integration(self):
        """Test that middleware integrates with rate limit config."""
        # Test that middleware respects the enabled setting during initialization
        with patch.object(rate_limit_config, 'enabled', False):
            middleware = RateLimitMiddleware(None)
            self.assertFalse(middleware._enabled)

        with patch.object(rate_limit_config, 'enabled', True):
            middleware = RateLimitMiddleware(None)
            self.assertTrue(middleware._enabled)


if __name__ == '__main__':
    unittest.main()
