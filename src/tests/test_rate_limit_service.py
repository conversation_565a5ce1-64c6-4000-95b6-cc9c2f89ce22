"""
Unit tests for rate limiting service.

Tests Redis backend, memory fallback, and service integration.
"""

import asyncio
import os
import sys
import time
import unittest
from unittest.mock import AsyncMock, MagicMock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.app.core.service.rate_limit_service import (
    RateLimitService,
    RedisRateLimitBackend,
    MemoryRateLimitBackend,
    RateLimitError
)
from src.app.core.config.rate_limit_config import rate_limit_config


class TestMemoryRateLimitBackend(unittest.TestCase):
    """Test in-memory rate limiting backend."""

    def setUp(self):
        self.backend = MemoryRateLimitBackend()

    def test_basic_rate_limiting(self):
        """Test basic rate limiting functionality."""
        async def run_test():
            key = "test_key"
            limit = 3
            window = 60

            # First 3 requests should be allowed
            for i in range(3):
                allowed, count, retry_after = await self.backend.check_rate_limit(key, limit, window)
                self.assertTrue(allowed)
                self.assertEqual(count, i + 1)
                self.assertEqual(retry_after, 0)

            # 4th request should be denied
            allowed, count, retry_after = await self.backend.check_rate_limit(key, limit, window)
            self.assertFalse(allowed)
            self.assertEqual(count, 3)
            self.assertGreater(retry_after, 0)

        asyncio.run(run_test())

    def test_different_keys_independent(self):
        """Test that different keys have independent rate limits."""
        async def run_test():
            limit = 2
            window = 60

            # Use up limit for key1
            await self.backend.check_rate_limit("key1", limit, window)
            await self.backend.check_rate_limit("key1", limit, window)

            # key1 should be denied
            allowed, _, _ = await self.backend.check_rate_limit("key1", limit, window)
            self.assertFalse(allowed)

            # key2 should still be allowed
            allowed, count, _ = await self.backend.check_rate_limit("key2", limit, window)
            self.assertTrue(allowed)
            self.assertEqual(count, 1)

        asyncio.run(run_test())


class TestRateLimitService(unittest.TestCase):
    """Test rate limiting service integration."""

    def setUp(self):
        self.service = RateLimitService()

    def test_disabled_rate_limiting(self):
        """Test behavior when rate limiting is disabled."""
        async def run_test():
            with patch.object(rate_limit_config, 'enabled', False):
                allowed, count, retry_after = await self.service.check_rate_limit("test", "/auth/login")

                self.assertTrue(allowed)
                self.assertEqual(count, 0)
                self.assertEqual(retry_after, 0)

        asyncio.run(run_test())

    def test_endpoint_specific_limits(self):
        """Test endpoint-specific rate limits."""
        async def run_test():
            with patch.object(rate_limit_config, 'enabled', True):
                with patch.object(rate_limit_config, 'get_endpoint_limit') as mock_get_limit:
                    mock_get_limit.return_value = 5

                    with patch.object(self.service, '_get_backend') as mock_backend:
                        mock_backend_instance = AsyncMock()
                        mock_backend_instance.check_rate_limit.return_value = (True, 1, 0)
                        mock_backend.return_value = mock_backend_instance

                        await self.service.check_rate_limit("test", "/auth/login")

                        mock_backend_instance.check_rate_limit.assert_called_once_with("test", 5, rate_limit_config.window_seconds)

        asyncio.run(run_test())


if __name__ == '__main__':
    unittest.main()

