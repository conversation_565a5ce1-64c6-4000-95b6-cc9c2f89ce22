"""
Unit tests for CSRF middleware functionality.
"""

import os
import json
import pytest
from unittest.mock import patch, MagicMock, AsyncMock

# Set up test environment variables before importing CSRF modules
os.environ.update({
    'CSRF_SECRET_KEY': 'test_secret_key_that_is_long_enough_for_security_requirements_and_testing',
    'CSRF_ENABLED': 'true',
    'CSRF_TOKEN_HEADER': 'X-CSRF-Token',
    'CSRF_COOKIE_NAME': 'csrf_token',
    'CSRF_TOKEN_EXPIRE_MINUTES': '60',
    'CSRF_EXEMPT_PATHS': '/auth/login,/auth/callback,/docs',
    'ENVIRONMENT': 'testing',
    # Auth config (required by middleware)
    'AUTH_SESSION_COOKIE_NAME': 'session_token',
    'AUTH_REFRESH_COOKIE_NAME': 'refresh_token'
})

try:
    from fastapi import Fast<PERSON><PERSON>, Request, Response
    from fastapi.testclient import TestClient
    from starlette.responses import <PERSON><PERSON><PERSON><PERSON>ponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from src.app.core.middleware.csrf_middleware import CSRFMiddleware
from src.app.core.security.csrf_service import csrf_service


@pytest.mark.skipif(not FASTAPI_AVAILABLE, reason="FastAPI not available")
class TestCSRFMiddleware:
    """Test cases for CSRF middleware."""

    def setup_method(self):
        """Set up test fixtures."""
        if not FASTAPI_AVAILABLE:
            pytest.skip("FastAPI not available")

        self.app = FastAPI()
        self.app.add_middleware(CSRFMiddleware)
        
        # Add test endpoints
        @self.app.get("/test-get")
        async def test_get():
            return {"message": "GET request"}
        
        @self.app.post("/test-post")
        async def test_post(data: dict = None):
            return {"message": "POST request", "data": data}
        
        @self.app.put("/test-put")
        async def test_put():
            return {"message": "PUT request"}
        
        @self.app.delete("/test-delete")
        async def test_delete():
            return {"message": "DELETE request"}
        
        @self.app.post("/auth/login")
        async def auth_login():
            return {"message": "Login endpoint"}
        
        self.client = TestClient(self.app)
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    def test_middleware_disabled(self, mock_config):
        """Test middleware behavior when CSRF is disabled."""
        mock_config.enabled = False
        
        # All requests should pass through
        response = self.client.post("/test-post", json={"data": "test"})
        assert response.status_code == 200
    
    def test_get_request_allowed(self):
        """Test that GET requests are not protected."""
        response = self.client.get("/test-get")
        assert response.status_code == 200
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    def test_exempt_path_allowed(self, mock_config):
        """Test that exempt paths are not protected."""
        mock_config.enabled = True
        mock_config.is_path_exempt.return_value = True
        
        response = self.client.post("/auth/login", json={"username": "test"})
        assert response.status_code == 200
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    @patch('src.app.core.middleware.csrf_middleware.csrf_service')
    def test_valid_token_in_header(self, mock_service, mock_config):
        """Test successful validation with token in header."""
        mock_config.enabled = True
        mock_config.token_header = "X-CSRF-Token"
        mock_config.is_path_exempt.return_value = False
        mock_service.validate_token.return_value = True
        
        headers = {"X-CSRF-Token": "valid_token"}
        response = self.client.post("/test-post", json={"data": "test"}, headers=headers)
        
        assert response.status_code == 200
        mock_service.validate_token.assert_called_once()
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    @patch('src.app.core.middleware.csrf_middleware.csrf_service')
    def test_valid_token_in_cookie(self, mock_service, mock_config):
        """Test successful validation with token in cookie."""
        mock_config.enabled = True
        mock_config.token_header = "X-CSRF-Token"
        mock_config.cookie_name = "csrf_token"
        mock_config.is_path_exempt.return_value = False
        mock_service.validate_token.return_value = True
        
        cookies = {"csrf_token": "valid_token"}
        response = self.client.post("/test-post", json={"data": "test"}, cookies=cookies)
        
        assert response.status_code == 200
        mock_service.validate_token.assert_called_once()
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    def test_missing_token_rejected(self, mock_config):
        """Test that requests without CSRF token are rejected."""
        mock_config.enabled = True
        mock_config.token_header = "X-CSRF-Token"
        mock_config.is_path_exempt.return_value = False
        
        response = self.client.post("/test-post", json={"data": "test"})
        
        assert response.status_code == 403
        assert "CSRF_TOKEN_INVALID" in response.json()["error"]
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    @patch('src.app.core.middleware.csrf_middleware.csrf_service')
    def test_invalid_token_rejected(self, mock_service, mock_config):
        """Test that requests with invalid CSRF token are rejected."""
        mock_config.enabled = True
        mock_config.token_header = "X-CSRF-Token"
        mock_config.is_path_exempt.return_value = False
        mock_service.validate_token.return_value = False
        
        headers = {"X-CSRF-Token": "invalid_token"}
        response = self.client.post("/test-post", json={"data": "test"}, headers=headers)
        
        assert response.status_code == 403
        assert "CSRF_TOKEN_INVALID" in response.json()["error"]
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    def test_all_protected_methods(self, mock_config):
        """Test that all state-changing methods are protected."""
        mock_config.enabled = True
        mock_config.token_header = "X-CSRF-Token"  # Add missing attribute
        mock_config.is_path_exempt.return_value = False
        
        protected_methods = ["POST", "PUT", "PATCH", "DELETE"]
        
        for method in protected_methods:
            if method == "POST":
                response = self.client.post("/test-post", json={"data": "test"})
            elif method == "PUT":
                response = self.client.put("/test-put", json={"data": "test"})
            elif method == "PATCH":
                response = self.client.patch("/test-patch", json={"data": "test"})
            elif method == "DELETE":
                response = self.client.delete("/test-delete")

            assert response.status_code == 403, f"{method} should be protected"
    
    @patch('src.app.core.middleware.csrf_middleware.csrf_config')
    @patch('src.app.core.middleware.csrf_middleware.csrf_service')
    def test_session_binding(self, mock_service, mock_config):
        """Test that session ID is extracted for token binding."""
        mock_config.enabled = True
        mock_config.token_header = "X-CSRF-Token"
        mock_config.is_path_exempt.return_value = False
        mock_service.validate_token.return_value = True
        
        # Mock auth config import
        with patch('src.app.core.config.auth_config.auth_config') as mock_auth_config:
            mock_auth_config.session_cookie_name = "session_id"
            mock_auth_config.refresh_cookie_name = "refresh_token"
            
            headers = {"X-CSRF-Token": "valid_token"}
            cookies = {"session_id": "test_session"}
            
            response = self.client.post(
                "/test-post", 
                json={"data": "test"}, 
                headers=headers, 
                cookies=cookies
            )
            
            assert response.status_code == 200
            # Verify session ID was passed to validation
            mock_service.validate_token.assert_called_with("valid_token", "test_session")
    
    def test_error_response_format(self):
        """Test the format of CSRF error responses."""
        with patch('src.app.core.middleware.csrf_middleware.csrf_config') as mock_config:
            mock_config.enabled = True
            mock_config.token_header = "X-CSRF-Token"
            mock_config.is_path_exempt.return_value = False
            
            response = self.client.post("/test-post", json={"data": "test"})
            
            assert response.status_code == 403
            
            error_data = response.json()
            assert "error" in error_data
            assert "message" in error_data
            assert "details" in error_data
            assert "required_header" in error_data["details"]
            assert "alternative" in error_data["details"]


@pytest.fixture
def mock_request():
    """Create a mock request for testing."""
    request = MagicMock(spec=Request)
    request.method = "POST"
    request.url.path = "/test-endpoint"
    request.headers = {}
    request.cookies = {}
    request.client.host = "127.0.0.1"
    return request


@pytest.fixture
def mock_call_next():
    """Create a mock call_next function."""
    async def call_next(request):
        return Response("OK", status_code=200)
    
    return call_next
