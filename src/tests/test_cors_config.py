"""
Tests for CORS Configuration

Tests the CORS configuration class and its validation logic.
"""

import os
import pytest
from unittest.mock import patch

from src.app.core.config.cors_config import CORSConfig, CORSConfigError


class TestCORSConfig:
    """Test CORS configuration functionality."""
    
    def test_default_development_config(self):
        """Test default CORS configuration in development environment."""
        with patch.dict(os.environ, {"ENVIRONMENT": "development"}, clear=False):
            config = CORSConfig()

            assert config.enabled is True
            assert len(config.allow_origins) > 0
            assert "http://localhost:3000" in config.allow_origins
            assert "GET" in config.allow_methods
            assert "POST" in config.allow_methods
            assert "Authorization" in config.allow_headers
            assert "Content-Type" in config.allow_headers
            assert config.allow_credentials is True
            assert config.max_age == 86400
    
    def test_production_requires_explicit_origins(self):
        """Test that production environment requires explicit origins."""
        with patch.dict(os.environ, {
            "ENVIRONMENT": "production",
            "CORS_ALLOW_ORIGINS": ""
        }, clear=False):
            with pytest.raises(CORSConfigError) as exc_info:
                CORSConfig()
            
            assert "explicit CORS_ALLOW_ORIGINS" in str(exc_info.value)
    
    def test_production_rejects_wildcard_origins(self):
        """Test that production environment rejects wildcard origins."""
        with patch.dict(os.environ, {
            "ENVIRONMENT": "production",
            "CORS_ALLOW_ORIGINS": "*"
        }, clear=False):
            with pytest.raises(CORSConfigError) as exc_info:
                CORSConfig()
            
            assert "Wildcard origin (*) is not allowed" in str(exc_info.value)
    
    def test_custom_origins_parsing(self):
        """Test parsing of custom origins from environment."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com,https://admin.example.com"
        }, clear=False):
            config = CORSConfig()
            
            assert "https://app.example.com" in config.allow_origins
            assert "https://admin.example.com" in config.allow_origins
            assert len(config.allow_origins) == 2
    
    def test_custom_methods_parsing(self):
        """Test parsing of custom methods from environment."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_METHODS": "GET,POST,OPTIONS"
        }, clear=False):
            config = CORSConfig()
            
            assert config.allow_methods == ["GET", "POST", "OPTIONS"]
    
    def test_custom_headers_parsing(self):
        """Test parsing of custom headers from environment."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_HEADERS": "Content-Type,Authorization,X-Custom-Header"
        }, clear=False):
            config = CORSConfig()
            
            expected_headers = ["Content-Type", "Authorization", "X-Custom-Header"]
            assert config.allow_headers == expected_headers
    
    def test_disabled_cors(self):
        """Test CORS disabled configuration."""
        with patch.dict(os.environ, {
            "CORS_ENABLED": "false"
        }, clear=False):
            config = CORSConfig()
            
            assert config.enabled is False
    
    def test_credentials_configuration(self):
        """Test credentials configuration."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_CREDENTIALS": "false"
        }, clear=False):
            config = CORSConfig()
            
            assert config.allow_credentials is False
    
    def test_max_age_configuration(self):
        """Test max age configuration."""
        with patch.dict(os.environ, {
            "CORS_MAX_AGE": "3600"
        }, clear=False):
            config = CORSConfig()
            
            assert config.max_age == 3600
    
    def test_expose_headers_parsing(self):
        """Test parsing of expose headers from environment."""
        with patch.dict(os.environ, {
            "CORS_EXPOSE_HEADERS": "X-Total-Count,X-Custom-Header"
        }, clear=False):
            config = CORSConfig()
            
            assert "X-Total-Count" in config.expose_headers
            assert "X-Custom-Header" in config.expose_headers
    
    def test_is_origin_allowed(self):
        """Test origin validation method."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com,https://admin.example.com"
        }, clear=False):
            config = CORSConfig()
            
            assert config.is_origin_allowed("https://app.example.com") is True
            assert config.is_origin_allowed("https://admin.example.com") is True
            assert config.is_origin_allowed("https://evil.com") is False
    
    def test_is_origin_allowed_with_wildcard(self):
        """Test origin validation with wildcard in development."""
        with patch.dict(os.environ, {
            "ENVIRONMENT": "development",
            "CORS_ALLOW_ORIGINS": "*"
        }, clear=False):
            config = CORSConfig()
            
            assert config.is_origin_allowed("https://any-domain.com") is True
            assert config.is_origin_allowed("http://localhost:3000") is True
    
    def test_is_origin_allowed_when_disabled(self):
        """Test origin validation when CORS is disabled."""
        with patch.dict(os.environ, {
            "CORS_ENABLED": "false"
        }, clear=False):
            config = CORSConfig()
            
            # When CORS is disabled, all origins should be allowed
            assert config.is_origin_allowed("https://any-domain.com") is True
    
    def test_negative_max_age_validation(self):
        """Test validation of negative max age."""
        with patch.dict(os.environ, {
            "CORS_MAX_AGE": "-1"
        }, clear=False):
            with pytest.raises(CORSConfigError) as exc_info:
                CORSConfig()
            
            assert "must be non-negative" in str(exc_info.value)
    
    def test_empty_methods_validation(self):
        """Test validation of empty methods."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_METHODS": ",,,"  # Only commas, results in empty list
        }, clear=False):
            with pytest.raises(CORSConfigError) as exc_info:
                CORSConfig()

            assert "cannot be empty" in str(exc_info.value)
    
    def test_empty_headers_validation(self):
        """Test validation of empty headers."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_HEADERS": ",,,"  # Only commas, results in empty list
        }, clear=False):
            with pytest.raises(CORSConfigError) as exc_info:
                CORSConfig()

            assert "cannot be empty" in str(exc_info.value)
    
    def test_get_config_summary(self):
        """Test configuration summary generation."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com"
        }, clear=False):
            config = CORSConfig()
            summary = config.get_config_summary()
            
            assert "cors" in summary
            assert summary["cors"]["enabled"] is True
            assert summary["cors"]["allow_origins_count"] == 1
            assert summary["cors"]["allow_credentials"] is True
