"""
Unit tests for CSRF service functionality.
"""

import os
import pytest
import time
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock

# Set up test environment variables before importing CSRF modules
# Force CSRF to be enabled for these specific tests
os.environ.update({
    'CSRF_SECRET_KEY': 'test_secret_key_that_is_long_enough_for_security_requirements_and_testing',
    'CSRF_ENABLED': 'true',  # Force enable for CSRF service tests
    'CSRF_TOKEN_HEADER': 'X-CSRF-Token',
    'CSRF_COOKIE_NAME': 'csrf_token',
    'CSRF_TOKEN_EXPIRE_MINUTES': '60',
    'CSRF_EXEMPT_PATHS': '/auth/login,/auth/callback,/docs',
    'ENVIRONMENT': 'testing'
})

from src.app.core.security.csrf_service import CSRFService, CSRFTokenError, CSRFValidationError
from src.app.core.config.csrf_config import csrf_config


class TestCSRFService:
    """Test cases for CSRF service."""

    def setup_method(self):
        """Set up test fixtures."""
        # Ensure CSRF is enabled for these tests
        csrf_config.enabled = True
        self.csrf_service = CSRFService()
    
    def test_generate_token_success(self):
        """Test successful token generation."""
        token = self.csrf_service.generate_token()
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_generate_token_with_session_id(self):
        """Test token generation with session ID binding."""
        session_id = "test_session_123"
        token = self.csrf_service.generate_token(session_id)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_validate_token_success(self):
        """Test successful token validation."""
        token = self.csrf_service.generate_token()
        
        is_valid = self.csrf_service.validate_token(token)
        assert is_valid is True
    
    def test_validate_token_with_session_id(self):
        """Test token validation with session ID binding."""
        session_id = "test_session_123"
        token = self.csrf_service.generate_token(session_id)
        
        # Valid with correct session ID
        is_valid = self.csrf_service.validate_token(token, session_id)
        assert is_valid is True
        
        # Invalid with wrong session ID
        is_valid = self.csrf_service.validate_token(token, "wrong_session")
        assert is_valid is False
    
    def test_validate_empty_token(self):
        """Test validation of empty token."""
        is_valid = self.csrf_service.validate_token("")
        assert is_valid is False
        
        is_valid = self.csrf_service.validate_token(None)
        assert is_valid is False
    
    def test_validate_invalid_token_format(self):
        """Test validation of malformed tokens."""
        # Too few parts
        is_valid = self.csrf_service.validate_token("invalid")
        assert is_valid is False
        
        # Invalid format
        is_valid = self.csrf_service.validate_token("part1:part2")
        assert is_valid is False
    
    def test_validate_token_invalid_signature(self):
        """Test validation of token with invalid signature."""
        token = self.csrf_service.generate_token()
        
        # Tamper with token
        tampered_token = token[:-5] + "XXXXX"
        
        is_valid = self.csrf_service.validate_token(tampered_token)
        assert is_valid is False
    
    def test_validate_token_when_disabled(self):
        """Test token validation when CSRF is disabled."""
        # Temporarily disable CSRF
        original_enabled = csrf_config.enabled
        csrf_config.enabled = False

        try:
            # Should return True regardless of token
            is_valid = self.csrf_service.validate_token("any_token")
            assert is_valid is True
        finally:
            # Restore original setting
            csrf_config.enabled = original_enabled
    
    def test_validate_expired_token(self):
        """Test validation of expired token."""
        # Create a service with very short expiration
        original_expire_minutes = csrf_config.token_expire_minutes
        csrf_config.token_expire_minutes = -1  # Already expired

        try:
            expired_service = CSRFService()
            token = expired_service.generate_token()

            # Reset to normal expiration for validation
            csrf_config.token_expire_minutes = 60

            is_valid = expired_service.validate_token(token)
            assert is_valid is False
        finally:
            # Restore original setting
            csrf_config.token_expire_minutes = original_expire_minutes
    
    def test_get_token_info_success(self):
        """Test extracting token information."""
        token = self.csrf_service.generate_token()
        
        info = self.csrf_service.get_token_info(token)
        
        assert info is not None
        assert 'timestamp' in info
        assert 'created_at' in info
        assert 'expires_at' in info
        assert 'is_expired' in info
        assert 'nonce' in info
        assert 'has_session_binding' in info
        
        assert info['is_expired'] is False
        assert info['has_session_binding'] is False
    
    def test_get_token_info_with_session(self):
        """Test extracting token information with session binding."""
        session_id = "test_session"
        token = self.csrf_service.generate_token(session_id)
        
        info = self.csrf_service.get_token_info(token)
        
        assert info is not None
        assert info['has_session_binding'] is True
    
    def test_get_token_info_invalid_token(self):
        """Test extracting info from invalid token."""
        info = self.csrf_service.get_token_info("invalid_token")
        assert info is None
        
        info = self.csrf_service.get_token_info("")
        assert info is None
    
    def test_token_uniqueness(self):
        """Test that generated tokens are unique."""
        tokens = set()
        
        for _ in range(100):
            token = self.csrf_service.generate_token()
            assert token not in tokens
            tokens.add(token)
    
    def test_token_generation_error_handling(self):
        """Test error handling in token generation."""
        with patch('secrets.token_urlsafe', side_effect=Exception("Random generation failed")):
            try:
                self.csrf_service.generate_token()
                assert False, "Should have raised CSRFTokenError"
            except CSRFTokenError:
                pass  # Expected

    def test_token_validation_error_handling(self):
        """Test error handling in token validation."""
        # Test with malformed token that causes exception
        with patch('hmac.compare_digest', side_effect=Exception("HMAC error")):
            try:
                self.csrf_service.validate_token("valid_looking_token:with:parts:signature")
                assert False, "Should have raised CSRFValidationError"
            except CSRFValidationError:
                pass  # Expected
