"""
Tests for CORS Middleware

Tests the CORS middleware functionality and header handling.
"""

import os
import pytest
from unittest.mock import patch
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient

from src.app.core.middleware.cors_middleware import CORSMiddleware


def create_app_with_cors():
    """Create a test FastAPI app with CORS middleware."""
    from src.app.core.config.cors_config import CORSConfig

    # Create fresh config for each test
    config = CORSConfig()

    app = FastAPI()
    app.add_middleware(CORSMiddleware, config=config)

    @app.get("/test")
    def test_endpoint():
        return {"message": "test"}

    @app.post("/test")
    def test_post_endpoint():
        return {"message": "posted"}

    return app


class TestCORSMiddleware:
    """Test CORS middleware functionality."""
    
    def test_cors_disabled(self):
        """Test that CORS middleware is bypassed when disabled."""
        with patch.dict(os.environ, {"CORS_ENABLED": "false"}, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)

            response = client.get("/test", headers={"Origin": "https://evil.com"})

            assert response.status_code == 200
            # No CORS headers should be present when disabled
            assert "Access-Control-Allow-Origin" not in response.headers
    
    def test_simple_cors_request(self):
        """Test simple CORS request with allowed origin."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)

            response = client.get("/test", headers={"Origin": "https://app.example.com"})

            assert response.status_code == 200
            assert response.headers["Access-Control-Allow-Origin"] == "https://app.example.com"
            assert response.headers["Access-Control-Allow-Credentials"] == "true"
            assert "Vary" in response.headers
            assert "Origin" in response.headers["Vary"]
    
    def test_cors_request_blocked_origin(self):
        """Test CORS request with blocked origin."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)

            response = client.get("/test", headers={"Origin": "https://evil.com"})

            assert response.status_code == 200
            # No CORS headers should be present for blocked origins
            assert "Access-Control-Allow-Origin" not in response.headers
    
    def test_cors_preflight_request(self):
        """Test CORS preflight (OPTIONS) request."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com",
            "CORS_ALLOW_METHODS": "GET,POST,PUT",
            "CORS_ALLOW_HEADERS": "Content-Type,Authorization"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            response = client.options("/test", headers={
                "Origin": "https://app.example.com",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type,Authorization"
            })
            
            assert response.status_code == 200
            assert response.headers["Access-Control-Allow-Origin"] == "https://app.example.com"
            assert response.headers["Access-Control-Allow-Methods"] == "GET, POST, PUT"
            assert "Content-Type" in response.headers["Access-Control-Allow-Headers"]
            assert "Authorization" in response.headers["Access-Control-Allow-Headers"]
            assert "Access-Control-Max-Age" in response.headers
    
    def test_cors_preflight_blocked_origin(self):
        """Test CORS preflight request with blocked origin."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            response = client.options("/test", headers={
                "Origin": "https://evil.com",
                "Access-Control-Request-Method": "POST"
            })
            
            assert response.status_code == 200
            # No CORS headers should be present for blocked origins
            assert "Access-Control-Allow-Origin" not in response.headers
            assert "Access-Control-Allow-Methods" not in response.headers
    
    def test_cors_wildcard_origin(self):
        """Test CORS with wildcard origin in development."""
        with patch.dict(os.environ, {
            "ENVIRONMENT": "development",
            "CORS_ALLOW_ORIGINS": "*",
            "CORS_ALLOW_CREDENTIALS": "false"  # Required for wildcard
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            response = client.get("/test", headers={"Origin": "https://any-domain.com"})
            
            assert response.status_code == 200
            assert response.headers["Access-Control-Allow-Origin"] == "*"
            assert "Access-Control-Allow-Credentials" not in response.headers
    
    def test_cors_expose_headers(self):
        """Test CORS expose headers functionality."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com",
            "CORS_EXPOSE_HEADERS": "X-Total-Count,X-Custom-Header"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            response = client.get("/test", headers={"Origin": "https://app.example.com"})
            
            assert response.status_code == 200
            assert "X-Total-Count" in response.headers["Access-Control-Expose-Headers"]
            assert "X-Custom-Header" in response.headers["Access-Control-Expose-Headers"]
    
    def test_cors_no_origin_header(self):
        """Test request without Origin header."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            response = client.get("/test")
            
            assert response.status_code == 200
            # No CORS headers should be present without Origin header
            assert "Access-Control-Allow-Origin" not in response.headers
    
    def test_cors_requested_headers_validation(self):
        """Test validation of requested headers in preflight."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com",
            "CORS_ALLOW_HEADERS": "Content-Type,Authorization"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            # Request with allowed headers
            response = client.options("/test", headers={
                "Origin": "https://app.example.com",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type,Authorization"
            })
            
            assert response.status_code == 200
            assert "Content-Type" in response.headers["Access-Control-Allow-Headers"]
            assert "Authorization" in response.headers["Access-Control-Allow-Headers"]
    
    def test_cors_requested_headers_partial_allowed(self):
        """Test preflight with mix of allowed and disallowed headers."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com",
            "CORS_ALLOW_HEADERS": "Content-Type,Authorization"
        }, clear=False):
            app = create_app_with_cors()
            client = TestClient(app)
            
            # Request with mix of allowed and disallowed headers
            response = client.options("/test", headers={
                "Origin": "https://app.example.com",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type,X-Forbidden-Header,Authorization"
            })
            
            assert response.status_code == 200
            allowed_headers = response.headers["Access-Control-Allow-Headers"]
            assert "Content-Type" in allowed_headers
            assert "Authorization" in allowed_headers
            assert "X-Forbidden-Header" not in allowed_headers
    
    def test_cors_vary_header_preservation(self):
        """Test that existing Vary header is preserved."""
        with patch.dict(os.environ, {
            "CORS_ALLOW_ORIGINS": "https://app.example.com"
        }, clear=False):
            from src.app.core.config.cors_config import CORSConfig
            config = CORSConfig()

            app = FastAPI()

            @app.middleware("http")
            async def add_vary_header(request, call_next):
                response = await call_next(request)
                response.headers["Vary"] = "Accept-Encoding"
                return response

            app.add_middleware(CORSMiddleware, config=config)

            @app.get("/test")
            def test_endpoint():
                return {"message": "test"}

            client = TestClient(app)
            
            response = client.get("/test", headers={"Origin": "https://app.example.com"})
            
            assert response.status_code == 200
            vary_header = response.headers["Vary"]
            assert "Accept-Encoding" in vary_header
            assert "Origin" in vary_header
