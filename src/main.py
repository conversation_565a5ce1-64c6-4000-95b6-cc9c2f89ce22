from fastapi import FastAPI

from src.app.core.config.log_config import configure_application_logging
configure_application_logging()

from src.app.api.router import (
    tenant_router, api_key_router, log_entry_router, user_router, notification_router,
    policy_router, project_router, rule_router, provider_config_router, keycloak_router,
    auth_router, payment_router, plan_router, subscription_router, usage_record_router,
    csrf_router, redis_monitor_router
)
from src.app.core.exception_handler.exception_handler import init_exception_handlers
from src.app.core.middleware.cors_middleware import CORSMiddleware
from src.app.core.middleware.csrf_middleware import CSRFMiddleware
from src.app.core.middleware.rate_limit_middleware import RateLimitMiddleware

app = FastAPI()

# Add CORS middleware (should be first to handle preflight requests)
app.add_middleware(CORSMiddleware)

# Add rate limiting middleware (should be early in the chain)
app.add_middleware(RateLimitMiddleware)

# Add CSRF protection middleware
app.add_middleware(CSRFMiddleware)

# Include routers
app.include_router(csrf_router.router)  # CSRF endpoints (should be early for easy access)
app.include_router(redis_monitor_router.router)  # Redis monitoring endpoints
app.include_router(auth_router.router)
app.include_router(api_key_router.router)
app.include_router(log_entry_router.router)
app.include_router(user_router.router)
app.include_router(notification_router.router)
app.include_router(payment_router.router)
app.include_router(plan_router.router)
app.include_router(policy_router.router)
app.include_router(subscription_router.router)
app.include_router(tenant_router.router)
app.include_router(project_router.router)
app.include_router(rule_router.router)
app.include_router(usage_record_router.router)
app.include_router(provider_config_router.router)
app.include_router(keycloak_router.router)

# Initialize exception handlers
init_exception_handlers(app)


@app.get("/")
def root():
    return {"message": "Hello, World!"}
