"""
Redis Monitoring Router

Provides endpoints to monitor Redis connection status and rate limiting backend.
Useful for debugging and monitoring Redis caching operations.
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status

from src.app.core.service.rate_limit_service import rate_limit_service
from src.app.core.config.rate_limit_config import rate_limit_config

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/redis-monitor",
    tags=["Redis Monitoring"],
    responses={404: {"description": "Not found"}},
)


@router.get("/status", response_model=Dict[str, Any])
async def get_redis_status():
    """
    Get Redis connection status and configuration.
    
    Returns information about:
    - Redis connection status
    - Current backend being used (Redis or memory)
    - Configuration details
    - Connection parameters (masked for security)
    """
    try:
        # Get rate limit info to determine backend
        test_key = "monitor:health_check"
        backend_info = await rate_limit_service.get_rate_limit_info(test_key)
        
        # Get configuration details
        config_summary = {
            "enabled": rate_limit_config.enabled,
            "fallback_to_memory": rate_limit_config.fallback_to_memory,
            "redis_url": _mask_redis_url(rate_limit_config.redis_url),
            "redis_db": rate_limit_config.redis_db,
            "max_connections": rate_limit_config.redis_max_connections,
            "ssl_enabled": rate_limit_config.redis_ssl,
            "window_seconds": rate_limit_config.window_seconds,
            "default_limit": rate_limit_config.default_limit,
        }
        
        # Test Redis connection
        redis_status = await _test_redis_connection()
        
        return {
            "timestamp": backend_info.get("current_time"),
            "backend": backend_info.get("backend", "unknown"),
            "redis_connection": redis_status,
            "configuration": config_summary,
            "backend_info": backend_info
        }
        
    except Exception as e:
        logger.error(f"Failed to get Redis status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Redis status: {str(e)}"
        )


@router.get("/test-connection")
async def test_redis_connection():
    """
    Test Redis connection by performing basic operations.
    
    This endpoint will:
    1. Test Redis connectivity
    2. Perform a set/get/delete operation
    3. Return detailed connection information
    """
    try:
        result = await _test_redis_connection()
        
        if result["connected"]:
            logger.info("🔴✅ Redis connection test successful via API")
            return {
                "status": "success",
                "message": "Redis connection is working properly",
                "details": result
            }
        else:
            logger.warning("🔴❌ Redis connection test failed via API")
            return {
                "status": "failed",
                "message": "Redis connection is not working",
                "details": result
            }
            
    except Exception as e:
        logger.error(f"Redis connection test error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Redis connection test failed: {str(e)}"
        )


@router.post("/test-rate-limit")
async def test_rate_limit(test_key: str = "api_test"):
    """
    Test rate limiting functionality.
    
    This will create a test rate limit entry and show which backend is being used.
    """
    try:
        # Test rate limit
        rate_limit_key = f"test:{test_key}"
        allowed, current, retry_after = await rate_limit_service.check_rate_limit(
            key=rate_limit_key,
            endpoint_path="/test"
        )
        
        # Get backend info
        backend_info = await rate_limit_service.get_rate_limit_info(rate_limit_key)
        backend_type = backend_info.get("backend", "unknown")
        
        logger.info(f"🧪 Rate limit test: backend={backend_type}, allowed={allowed}, current={current}")
        
        return {
            "status": "success",
            "rate_limit_result": {
                "allowed": allowed,
                "current_count": current,
                "retry_after": retry_after,
                "backend": backend_type
            },
            "backend_info": backend_info
        }
        
    except Exception as e:
        logger.error(f"Rate limit test error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Rate limit test failed: {str(e)}"
        )


async def _test_redis_connection() -> Dict[str, Any]:
    """Test Redis connection and return detailed status."""
    try:
        # Try to get the Redis backend directly
        from src.app.core.service.rate_limit_service import rate_limit_service
        backend = await rate_limit_service._get_backend()
        
        # Check if it's Redis backend
        if hasattr(backend, '_redis') and backend._redis:
            try:
                # Test ping
                pong = await backend._redis.ping()
                
                # Test basic operations
                test_key = "monitor:connection_test"
                await backend._redis.set(test_key, "test_value", ex=10)
                value = await backend._redis.get(test_key)
                await backend._redis.delete(test_key)
                
                return {
                    "connected": True,
                    "ping_result": pong,
                    "test_operation": "success",
                    "backend_type": "redis"
                }
            except Exception as e:
                return {
                    "connected": False,
                    "error": str(e),
                    "backend_type": "redis_failed"
                }
        else:
            return {
                "connected": False,
                "backend_type": "memory_fallback",
                "message": "Using memory backend (Redis not available)"
            }
            
    except Exception as e:
        return {
            "connected": False,
            "error": str(e),
            "backend_type": "unknown"
        }


def _mask_redis_url(url: str) -> str:
    """Mask sensitive information in Redis URL."""
    if not url:
        return "not_configured"
    
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        
        if parsed.password:
            masked_url = url.replace(parsed.password, "***")
        else:
            masked_url = url
            
        return masked_url
    except Exception:
        return "rediss://***:***@***:****/***"
