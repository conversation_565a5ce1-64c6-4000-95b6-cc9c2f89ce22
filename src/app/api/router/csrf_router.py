"""
CSRF Token Management Router

Provides endpoints for CSRF token generation and information.
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, Request, Response, HTTPException, status

from src.app.core.config.csrf_config import csrf_config
from src.app.core.security.csrf_dependencies import generate_csrf_token, CSRFTokenResponse
from src.app.core.security.csrf_service import csrf_service

router = APIRouter(
    prefix="/csrf",
    tags=["CSRF Protection"],
    responses={
        403: {"description": "CSRF protection disabled or validation failed"},
        500: {"description": "Internal server error"}
    }
)


@router.get(
    "/token",
    response_model=Dict[str, Any],
    summary="Generate CSRF Token",
    description="Generate a new CSRF token for protecting state-changing requests"
)
async def get_csrf_token(
    request: Request,
    response: Response,
    csrf_token: CSRFTokenResponse = Depends(generate_csrf_token)
) -> Dict[str, Any]:
    """
    Generate a new CSRF token.
    
    Returns a CSRF token that must be included in subsequent state-changing requests
    (POST, PUT, PATCH, DELETE) either as a header or in the JSON body.
    
    The token is bound to the current session (if authenticated) and has a limited lifetime.
    """
    if not csrf_config.enabled:
        return {
            "csrf_protection": "disabled",
            "message": "CSRF protection is currently disabled",
            "token": None
        }
    
    return {
        "csrf_token": csrf_token.token,
        "expires_in_minutes": csrf_token.expires_in_minutes,
        "usage": {
            "header_name": csrf_token.header_name,
            "header_example": f"{csrf_token.header_name}: {csrf_token.token}",
            "json_body_example": {
                "csrf_token": csrf_token.token,
                "your_data": "..."
            }
        },
        "cookie_name": csrf_token.cookie_name if csrf_config.cookie_name else None
    }


@router.get(
    "/info",
    response_model=Dict[str, Any],
    summary="CSRF Configuration Info",
    description="Get information about CSRF protection configuration"
)
async def get_csrf_info() -> Dict[str, Any]:
    """
    Get CSRF protection configuration information.
    
    Returns current CSRF settings and requirements for client applications.
    """
    return {
        "csrf_protection": {
            "enabled": csrf_config.enabled,
            "token_header": csrf_config.token_header,
            "cookie_name": csrf_config.cookie_name,
            "token_expire_minutes": csrf_config.token_expire_minutes,
            "exempt_paths": csrf_config.exempt_paths
        },
        "requirements": {
            "protected_methods": ["POST", "PUT", "PATCH", "DELETE"],
            "token_sources": [
                f"HTTP Header: {csrf_config.token_header}",
                "JSON Body: csrf_token field",
                f"Cookie: {csrf_config.cookie_name} (less secure)"
            ],
            "session_binding": "Tokens are bound to authenticated sessions when available"
        },
        "usage_examples": {
            "header": f"{csrf_config.token_header}: your_csrf_token_here",
            "json_body": {
                "csrf_token": "your_csrf_token_here",
                "your_request_data": "..."
            },
            "curl_example": f"curl -X POST -H '{csrf_config.token_header}: your_token' -H 'Content-Type: application/json' -d '{{\"data\": \"value\"}}' /api/endpoint"
        }
    }


@router.post(
    "/validate",
    response_model=Dict[str, Any],
    summary="Validate CSRF Token",
    description="Validate a CSRF token (for testing purposes)"
)
async def validate_csrf_token(
    request: Request,
    token_data: Dict[str, str]
) -> Dict[str, Any]:
    """
    Validate a CSRF token.
    
    This endpoint is primarily for testing and debugging purposes.
    In normal operation, CSRF validation happens automatically via middleware.
    
    Request body should contain:
    {
        "csrf_token": "your_token_here"
    }
    """
    if not csrf_config.enabled:
        return {
            "valid": True,
            "message": "CSRF protection is disabled",
            "csrf_protection": "disabled"
        }
    
    token = token_data.get("csrf_token")
    if not token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="csrf_token is required in request body"
        )
    
    try:
        # Extract session ID for validation
        from src.app.core.security.csrf_dependencies import _extract_session_id
        session_id = _extract_session_id(request)
        
        # Validate token
        is_valid = csrf_service.validate_token(token, session_id)
        
        # Get token info
        token_info = csrf_service.get_token_info(token)
        
        return {
            "valid": is_valid,
            "token_info": token_info,
            "session_binding": session_id is not None,
            "message": "Token is valid" if is_valid else "Token validation failed"
        }
        
    except Exception as e:
        return {
            "valid": False,
            "error": str(e),
            "message": "Token validation failed due to error"
        }
