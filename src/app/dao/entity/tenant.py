from typing import List, Optional, TYPE_CHECKING
from datetime import datetime, timezone

from sqlalchemy import Column, DateTime
from sqlmodel import SQLModel, Field, Relationship

# Note: Using string fields instead of enum types to avoid database enum dependencies
# Validation is handled at the API layer using Pydantic enums

if TYPE_CHECKING:
    from src.app.dao.entity.api_key import ApiKey
    from src.app.dao.entity.log_entry import LogEntry
    from src.app.dao.entity.notification_event import NotificationEvent
    from src.app.dao.entity.payment import Payment
    from src.app.dao.entity.policy import Policy
    from src.app.dao.entity.provider_config import ProviderConfig
    from src.app.dao.entity.project import Project
    from src.app.dao.entity.tenant_keycloak_config import TenantKeycloakConfig
    from src.app.dao.entity.subscription import Subscription
    from src.app.dao.entity.user import User


class Tenant(SQLModel, table=True):
    __tablename__ = "tenant"

    id: Optional[int] = Field(default=None, primary_key=True)
    organization_name: str = Field(index=True, nullable=False)
    industry: str = Field(
        default=None,
        description="The industry the tenant operates in, e.g., healthcare, finance, etc.",
        nullable=True
    )
    region_preference: str = Field(max_length=50, nullable=False)
    subscription_level: str = Field(max_length=50, nullable=False)

    status: str = Field(default="PENDING", max_length=20, nullable=False, description="Tenant status: PENDING, ACTIVE, SUSPENDED")
    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    updated_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )

    users: List["User"] = Relationship(back_populates="tenant")
    api_keys: List["ApiKey"] = Relationship(back_populates="tenant")
    policies: List["Policy"] = Relationship(back_populates="tenant")
    log_entries: List["LogEntry"] = Relationship(back_populates="tenant")
    provider_configs: List["ProviderConfig"] = Relationship(back_populates="tenant")
    notifications: List["NotificationEvent"] = Relationship(back_populates="tenant")
    projects: List["Project"] = Relationship(back_populates="tenant")
    keycloak_config: Optional["TenantKeycloakConfig"] = Relationship(back_populates="tenant")

    subscriptions: List["Subscription"] = Relationship(back_populates="tenant")
    payments: List["Payment"] = Relationship(back_populates="tenant")
