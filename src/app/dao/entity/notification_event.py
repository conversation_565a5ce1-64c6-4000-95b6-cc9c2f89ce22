from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING

from sqlalchemy import Column, DateTime
from sqlmodel import SQLModel, Field, Relationship  


if TYPE_CHECKING:
    from src.app.dao.entity.api_key import ApiKey
    from src.app.dao.entity.tenant import Tenant
    from src.app.dao.entity.user import User


class NotificationEvent(SQLModel, table=True):
    __tablename__ = "notification_event"

    id: Optional[int] = Field(default=None, primary_key=True)
    event_type: str = Field(max_length=50, nullable=False)
    details: Optional[str] = Field(default=None, description="Free‐text details or JSON blob")
    sent_to: Optional[str] = Field(default=None, description="E.g., email address or webhook URL")
    timestamp: datetime = Field(
        sa_column=Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    )
    tenant_id: int = Field(foreign_key="tenant.id", nullable=False, index=True)
    tenant: "Tenant" = Relationship(back_populates="notifications")
    user_id: Optional[int] = Field(default=None, foreign_key="user.id", index=True)
    user: Optional["User"] = Relationship()
    api_key_id: Optional[int] = Field(default=None, foreign_key="api_key.id", index=True)
    api_key: Optional["ApiKey"] = Relationship()
