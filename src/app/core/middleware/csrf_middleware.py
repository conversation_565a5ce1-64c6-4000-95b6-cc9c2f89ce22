"""
CSRF Protection Middleware

FastAPI middleware for automatic CSRF token validation on state-changing requests.
"""

import json
import logging
from typing import Callable, Optional, Dict, Any
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.app.core.config.csrf_config import csrf_config
from src.app.core.security.csrf_service import csrf_service, CSRFValidationError

logger = logging.getLogger(__name__)


class CSRFMiddleware(BaseHTTPMiddleware):
    """
    CSRF protection middleware for FastAPI applications.
    
    Automatically validates CSRF tokens for state-changing HTTP methods
    (POST, PUT, PATCH, DELETE) while allowing exemptions for specific paths.
    """
    
    # HTTP methods that require CSRF protection
    PROTECTED_METHODS = {"POST", "PUT", "PATCH", "DELETE"}
    
    def __init__(self, app, **kwargs):
        super().__init__(app, **kwargs)
        self._enabled = csrf_config.enabled
        self._token_header = csrf_config.token_header
        self._cookie_name = csrf_config.cookie_name
        
        if self._enabled:
            logger.info(f"CSRF middleware enabled - protecting {', '.join(self.PROTECTED_METHODS)} requests")
        else:
            logger.info("CSRF middleware disabled")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and validate CSRF token if required.
        
        Args:
            request: The incoming HTTP request
            call_next: The next middleware/handler in the chain
            
        Returns:
            HTTP response
        """
        # Skip if CSRF is disabled
        if not self._enabled:
            return await call_next(request)
        
        # Skip if method doesn't require protection
        if request.method not in self.PROTECTED_METHODS:
            return await call_next(request)
        
        # Skip if path is exempt
        if csrf_config.is_path_exempt(request.url.path):
            logger.debug(f"CSRF check skipped for exempt path: {request.url.path}")
            return await call_next(request)
        
        # Validate CSRF token
        try:
            if not await self._validate_csrf_token(request):
                logger.warning(
                    f"CSRF validation failed for {request.method} {request.url.path} "
                    f"from {request.client.host if request.client else 'unknown'}"
                )
                return self._create_csrf_error_response()
            
            logger.debug(f"CSRF validation passed for {request.method} {request.url.path}")
            
        except CSRFValidationError as e:
            logger.error(f"CSRF validation error: {e}")
            return self._create_csrf_error_response()
        except Exception as e:
            logger.error(f"Unexpected error in CSRF middleware: {e}")
            return self._create_csrf_error_response()
        
        return await call_next(request)
    
    async def _validate_csrf_token(self, request: Request) -> bool:
        """
        Validate CSRF token from request headers or JSON body.
        
        Args:
            request: The HTTP request to validate
            
        Returns:
            True if token is valid, False otherwise
        """
        # Try to get token from header first
        token = request.headers.get(self._token_header)
        
        # If not in header, try JSON body
        if not token:
            token = await self._extract_token_from_body(request)
        
        # If still no token, try cookie (less secure, mainly for debugging)
        if not token:
            token = request.cookies.get(self._cookie_name)
        
        if not token:
            logger.debug("No CSRF token found in request")
            return False
        
        # Get session ID for token binding (if available)
        session_id = self._extract_session_id(request)
        
        # Validate token
        return csrf_service.validate_token(token, session_id)
    
    async def _extract_token_from_body(self, request: Request) -> Optional[str]:
        """
        Extract CSRF token from JSON request body.
        
        Args:
            request: The HTTP request
            
        Returns:
            CSRF token if found, None otherwise
        """
        try:
            # Check if request has JSON content type
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith("application/json"):
                return None
            
            # Read and parse JSON body
            body = await request.body()
            if not body:
                return None
            
            try:
                json_data = json.loads(body)
                if isinstance(json_data, dict):
                    return json_data.get("csrf_token")
            except json.JSONDecodeError:
                logger.debug("Failed to parse JSON body for CSRF token")
                return None
                
        except Exception as e:
            logger.debug(f"Error extracting CSRF token from body: {e}")
            return None
        
        return None
    
    def _extract_session_id(self, request: Request) -> Optional[str]:
        """
        Extract session ID from request for token binding.
        
        Args:
            request: The HTTP request
            
        Returns:
            Session ID if available, None otherwise
        """
        # Try to get session ID from auth cookies
        from src.app.core.config.auth_config import auth_config
        
        session_cookie = request.cookies.get(auth_config.session_cookie_name)
        if session_cookie:
            return session_cookie
        
        refresh_cookie = request.cookies.get(auth_config.refresh_cookie_name)
        if refresh_cookie:
            return refresh_cookie
        
        return None
    
    def _create_csrf_error_response(self) -> JSONResponse:
        """
        Create standardized CSRF error response.
        
        Returns:
            JSON error response
        """
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "error": "CSRF_TOKEN_INVALID",
                "message": "CSRF token validation failed",
                "details": {
                    "required_header": self._token_header,
                    "alternative": "Include csrf_token in JSON body",
                    "documentation": "See API documentation for CSRF protection details"
                }
            }
        )
