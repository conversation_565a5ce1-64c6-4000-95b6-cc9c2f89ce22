"""
CORS Middleware

FastAPI middleware for Cross-Origin Resource Sharing (CORS) with secure defaults.
"""

import logging
from typing import Callable, Optional, List
from fastapi import Request, Response
from fastapi.responses import Response as FastAPIResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.app.core.config.cors_config import cors_config

logger = logging.getLogger(__name__)


class CORSMiddleware(BaseHTTPMiddleware):
    """
    CORS middleware for FastAPI applications.
    
    Handles Cross-Origin Resource Sharing with secure defaults and
    environment-aware configuration.
    """
    
    def __init__(self, app, config=None, **kwargs):
        super().__init__(app, **kwargs)
        # Allow injecting config for testing
        self._config = config or cors_config

        self._enabled = self._config.enabled
        self._allow_origins = self._config.allow_origins
        self._allow_methods = self._config.allow_methods
        self._allow_headers = self._config.allow_headers
        self._allow_credentials = self._config.allow_credentials
        self._expose_headers = self._config.expose_headers
        self._max_age = self._config.max_age

        if self._enabled:
            logger.info(f"CORS middleware enabled")
            logger.info(f"Allowed origins: {len(self._allow_origins)} configured")
            logger.info(f"Allowed methods: {', '.join(self._allow_methods)}")
            logger.info(f"Credentials allowed: {self._allow_credentials}")
        else:
            logger.info("CORS middleware disabled")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and add CORS headers if required.
        
        Args:
            request: The incoming HTTP request
            call_next: The next middleware/handler in the chain
            
        Returns:
            HTTP response with CORS headers
        """
        # Skip if CORS is disabled
        if not self._enabled:
            return await call_next(request)
        
        # Get origin from request
        origin = request.headers.get("origin")
        
        # Handle preflight requests (OPTIONS)
        if request.method == "OPTIONS":
            return self._handle_preflight_request(request, origin)
        
        # Process actual request
        response = await call_next(request)
        
        # Add CORS headers to response
        self._add_cors_headers(response, origin, request)
        
        return response
    
    def _handle_preflight_request(self, request: Request, origin: Optional[str]) -> Response:
        """
        Handle CORS preflight (OPTIONS) requests.
        
        Args:
            request: The preflight request
            origin: The origin header value
            
        Returns:
            Preflight response with appropriate CORS headers
        """
        # Create preflight response
        response = FastAPIResponse(status_code=200)
        
        # Add CORS headers
        self._add_cors_headers(response, origin, request, is_preflight=True)
        
        # Add preflight-specific headers
        if self._is_origin_allowed(origin):
            # Access-Control-Allow-Methods
            response.headers["Access-Control-Allow-Methods"] = ", ".join(self._allow_methods)
            
            # Access-Control-Allow-Headers
            requested_headers = request.headers.get("access-control-request-headers")
            if requested_headers:
                # Validate requested headers against allowed headers
                requested_header_list = [h.strip() for h in requested_headers.split(",")]
                allowed_requested_headers = [
                    h for h in requested_header_list 
                    if h.lower() in [ah.lower() for ah in self._allow_headers]
                ]
                
                if allowed_requested_headers:
                    response.headers["Access-Control-Allow-Headers"] = ", ".join(allowed_requested_headers)
                else:
                    # If no requested headers are allowed, still provide the default allowed headers
                    response.headers["Access-Control-Allow-Headers"] = ", ".join(self._allow_headers)
            else:
                response.headers["Access-Control-Allow-Headers"] = ", ".join(self._allow_headers)
            
            # Access-Control-Max-Age
            response.headers["Access-Control-Max-Age"] = str(self._max_age)
        
        logger.debug(f"Handled preflight request from origin: {origin}")
        return response
    
    def _add_cors_headers(
        self, 
        response: Response, 
        origin: Optional[str], 
        request: Request,
        is_preflight: bool = False
    ) -> None:
        """
        Add CORS headers to response.
        
        Args:
            response: The HTTP response
            origin: The origin header value
            request: The original request
            is_preflight: Whether this is a preflight response
        """
        if not self._is_origin_allowed(origin):
            logger.debug(f"Origin not allowed: {origin}")
            return
        
        # Access-Control-Allow-Origin
        if "*" in self._allow_origins and not self._allow_credentials:
            response.headers["Access-Control-Allow-Origin"] = "*"
        elif origin:
            response.headers["Access-Control-Allow-Origin"] = origin
        
        # Access-Control-Allow-Credentials
        if self._allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"
        
        # Access-Control-Expose-Headers (for actual requests, not preflight)
        if not is_preflight and self._expose_headers:
            response.headers["Access-Control-Expose-Headers"] = ", ".join(self._expose_headers)
        
        # Vary header to indicate that the response varies based on Origin
        existing_vary = response.headers.get("Vary", "")
        if existing_vary:
            if "Origin" not in existing_vary:
                response.headers["Vary"] = f"{existing_vary}, Origin"
        else:
            response.headers["Vary"] = "Origin"
    
    def _is_origin_allowed(self, origin: Optional[str]) -> bool:
        """
        Check if the given origin is allowed.
        
        Args:
            origin: The origin to check
            
        Returns:
            True if origin is allowed, False otherwise
        """
        if not origin:
            return False
        
        # Check if wildcard is allowed
        if "*" in self._allow_origins:
            return True
        
        # Check exact match
        return origin in self._allow_origins
    
    def _log_cors_request(self, request: Request, origin: Optional[str], allowed: bool) -> None:
        """
        Log CORS request details for debugging.
        
        Args:
            request: The HTTP request
            origin: The origin header value
            allowed: Whether the origin was allowed
        """
        status = "ALLOWED" if allowed else "BLOCKED"
        logger.debug(
            f"CORS {status}: {request.method} {request.url.path} "
            f"from origin: {origin or 'None'}"
        )
