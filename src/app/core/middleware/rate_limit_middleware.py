"""
Rate Limiting Middleware

FastAPI middleware for rate limiting critical endpoints.
Implements per-IP and per-tenant rate limiting with Redis backend.
"""

import logging
import time
from typing import Callable, Optional, Dict, Any

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.app.core.config.rate_limit_config import rate_limit_config
from src.app.core.service.rate_limit_service import rate_limit_service, RateLimitExceeded

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware for FastAPI applications.
    
    Protects critical endpoints from brute force attacks and abuse.
    Uses Redis for distributed rate limiting with in-memory fallback.
    """
    
    def __init__(self, app, **kwargs):
        super().__init__(app, **kwargs)
        self._enabled = rate_limit_config.enabled
        
        if self._enabled:
            logger.info(f"Rate limiting middleware enabled - protecting critical endpoints")
            logger.info(f"Default rate limit: {rate_limit_config.default_limit} requests per {rate_limit_config.window_seconds}s")
        else:
            logger.info("Rate limiting middleware disabled")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request and apply rate limiting if required.
        
        Args:
            request: The incoming HTTP request
            call_next: The next middleware/handler in the chain
            
        Returns:
            HTTP response
        """
        # Skip if rate limiting is disabled
        if not self._enabled:
            return await call_next(request)
        
        # Get client IP address
        client_ip = self._get_client_ip(request)
        
        # Get endpoint path for rate limit lookup
        endpoint_path = self._get_normalized_path(request.url.path)
        
        # Skip if not a critical endpoint
        if not rate_limit_config.is_critical_endpoint(endpoint_path):
            return await call_next(request)
        
        # Generate rate limit key
        rate_limit_key = f"ratelimit:{client_ip}:{endpoint_path}"
        
        try:
            # Check rate limit
            allowed, current, retry_after = await rate_limit_service.check_rate_limit(
                key=rate_limit_key,
                endpoint_path=endpoint_path
            )

            # Get backend info for logging
            backend_info = await rate_limit_service.get_rate_limit_info(rate_limit_key)
            backend_type = backend_info.get("backend", "unknown")
            backend_emoji = "🔴" if backend_type == "redis" else "💾"

            if not allowed:
                logger.warning(
                    f"{backend_emoji} Rate limit exceeded for {endpoint_path} from {client_ip} "
                    f"({current} requests, retry after {retry_after}s) [Backend: {backend_type}]"
                )
                return self._create_rate_limit_response(retry_after)
            else:
                # Log successful rate limit check (debug level)
                logger.debug(
                    f"{backend_emoji} Rate limit OK for {endpoint_path} from {client_ip} "
                    f"({current} requests) [Backend: {backend_type}]"
                )
            
            # Rate limit not exceeded, proceed with request
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # Add rate limit headers to response
            limit = rate_limit_config.get_endpoint_limit(endpoint_path)
            remaining = max(0, limit - current)
            
            response.headers["X-RateLimit-Limit"] = str(limit)
            response.headers["X-RateLimit-Remaining"] = str(remaining)
            response.headers["X-RateLimit-Reset"] = str(int(time.time() + rate_limit_config.window_seconds))
            
            # Log slow requests
            if process_time > 1.0:
                logger.warning(f"Slow request: {request.method} {endpoint_path} took {process_time:.2f}s")
            
            return response
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # On error, allow the request but log the issue
            return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request.
        
        Handles X-Forwarded-For and other proxy headers.
        """
        # Check X-Forwarded-For header (common with proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Get the first IP in the chain (client IP)
            return forwarded_for.split(",")[0].strip()
        
        # Fall back to direct client IP
        client_host = request.client.host if request.client else "unknown"
        return client_host
    
    def _get_normalized_path(self, path: str) -> str:
        """
        Normalize path for rate limiting.
        
        Removes path parameters to group similar endpoints.
        """
        # Handle tenant creation endpoint
        if path == "/tenant" or path.startswith("/tenant/"):
            if path == "/tenant" or path == "/tenant/verify-email":
                return path
            return "/tenant/{id}"
        
        # Handle auth endpoints
        if path.startswith("/auth/"):
            # Keep specific auth endpoints
            if path in ["/auth/login", "/auth/callback", "/auth/refresh", "/auth/logout"]:
                return path
            return "/auth/{endpoint}"
        
        # Return original path for other endpoints
        return path
    
    def _create_rate_limit_response(self, retry_after: int) -> JSONResponse:
        """
        Create rate limit exceeded response.
        
        Args:
            retry_after: Seconds until rate limit resets
            
        Returns:
            JSON response with 429 status code
        """
        response = JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "detail": "Rate limit exceeded. Please try again later.",
                "error": "too_many_requests",
                "retry_after": retry_after
            }
        )
        
        # Add standard rate limit headers
        response.headers["Retry-After"] = str(retry_after)
        
        return response
