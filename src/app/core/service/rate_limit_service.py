"""
Rate Limiting Service

Redis-backed rate limiting service with in-memory fallback.
Implements sliding window rate limiting for critical endpoints.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from collections import OrderedDict
from typing import Dict, Optional, Tuple, Any

import redis.asyncio as redis
from redis.asyncio import Redis

from src.app.core.config.rate_limit_config import rate_limit_config

logger = logging.getLogger(__name__)


class RateLimitError(Exception):
    """Rate limiting error."""
    pass


class RateLimitExceeded(Exception):
    """Rate limit exceeded error."""
    
    def __init__(self, message: str, retry_after: int):
        super().__init__(message)
        self.retry_after = retry_after


class RateLimitBackend(ABC):
    """Abstract base class for rate limiting backends."""
    
    @abstractmethod
    async def check_rate_limit(self, key: str, limit: int, window_seconds: int) -> Tuple[bool, int, int]:
        """
        Check if rate limit is exceeded.
        
        Args:
            key: Rate limit key (e.g., IP address)
            limit: Maximum requests allowed
            window_seconds: Time window in seconds
            
        Returns:
            Tuple of (allowed, current_count, retry_after_seconds)
        """
        pass
    
    @abstractmethod
    async def reset_rate_limit(self, key: str) -> None:
        """Reset rate limit for a key."""
        pass
    
    @abstractmethod
    async def get_rate_limit_info(self, key: str, window_seconds: int) -> Dict[str, Any]:
        """Get current rate limit information for a key."""
        pass


class RedisRateLimitBackend(RateLimitBackend):
    """Redis-backed rate limiting using sliding window."""
    
    def __init__(self):
        self._redis: Optional[Redis] = None
        self._connection_lock: Optional[asyncio.Lock] = None
        self._last_connection_attempt = 0
        self._connection_retry_delay = 30  # seconds
    
    async def _get_redis_connection(self) -> Optional[Redis]:
        """Get Redis connection with retry logic."""
        if self._redis and not self._redis.connection_pool.connection_kwargs.get('retry_on_timeout'):
            try:
                await self._redis.ping()
                return self._redis
            except Exception:
                self._redis = None
        
        current_time = time.time()
        if (current_time - self._last_connection_attempt) < self._connection_retry_delay:
            return None
        
        if self._connection_lock is None:
            self._connection_lock = asyncio.Lock()

        async with self._connection_lock:
            if self._redis:
                return self._redis
            
            try:
                self._last_connection_attempt = current_time
                connection_params = rate_limit_config.get_redis_connection_params()

                # Handle SSL connections properly
                if connection_params.get('ssl'):
                    # For SSL connections, construct the URL with SSL parameters
                    # and use from_url with SSL-specific parameters
                    ssl_params = {
                        'url': connection_params['url'],
                        'db': connection_params.get('db', 0),
                        'password': connection_params.get('password'),
                        'decode_responses': connection_params.get('decode_responses', True),
                        'socket_connect_timeout': connection_params.get('socket_connect_timeout', 10),
                        'socket_timeout': connection_params.get('socket_timeout', 10),
                        'retry_on_timeout': connection_params.get('retry_on_timeout', True),
                        'max_connections': connection_params.get('max_connections', 10),
                        'ssl_cert_reqs': connection_params.get('ssl_cert_reqs')
                    }
                    self._redis = await redis.from_url(**ssl_params)
                else:
                    # Use from_url for non-SSL connections
                    self._redis = await redis.from_url(**connection_params)

                await self._redis.ping()

                logger.info("Successfully connected to Redis for rate limiting")
                return self._redis

            except Exception as e:
                logger.warning(f"Failed to connect to Redis for rate limiting: {e}")
                self._redis = None
                return None
    
    async def check_rate_limit(self, key: str, limit: int, window_seconds: int) -> Tuple[bool, int, int]:
        """Check rate limit using Redis sliding window."""
        redis = await self._get_redis_connection()
        if not redis:
            raise RateLimitError("Redis connection unavailable")

        try:
            current_time = time.time()
            window_start = current_time - window_seconds

            # Log Redis operation for monitoring
            logger.debug(f"🔴 Redis rate limit check: key={key}, limit={limit}, window={window_seconds}s")

            # Use Redis pipeline for atomic operations
            pipe = redis.pipeline()

            # Remove expired entries
            pipe.zremrangebyscore(key, 0, window_start)

            # Count current requests in window
            pipe.zcard(key)

            # Add current request
            pipe.zadd(key, {str(current_time): current_time})

            # Set expiration
            pipe.expire(key, window_seconds + 1)

            results = await pipe.execute()
            current_count = results[1] + 1  # +1 for the request we just added

            if current_count > limit:
                # Remove the request we just added since it's rejected
                await redis.zrem(key, str(current_time))

                # Calculate retry after (time until oldest request expires)
                oldest_scores = await redis.zrange(key, 0, 0, withscores=True)
                if oldest_scores:
                    oldest_time = oldest_scores[0][1]
                    retry_after = max(1, int(oldest_time + window_seconds - current_time))
                else:
                    retry_after = window_seconds

                logger.info(f"🔴 Redis rate limit EXCEEDED: key={key}, count={current_count}/{limit}, retry_after={retry_after}s")
                return False, current_count - 1, retry_after

            logger.debug(f"🔴 Redis rate limit OK: key={key}, count={current_count}/{limit}")
            return True, current_count, 0

        except Exception as e:
            logger.error(f"Redis rate limit check failed: {e}")
            raise RateLimitError(f"Rate limit check failed: {e}")
    
    async def reset_rate_limit(self, key: str) -> None:
        """Reset rate limit for a key."""
        redis = await self._get_redis_connection()
        if redis:
            try:
                await redis.delete(key)
            except Exception as e:
                logger.error(f"Failed to reset rate limit for {key}: {e}")
    
    async def get_rate_limit_info(self, key: str, window_seconds: int) -> Dict[str, Any]:
        """Get rate limit information."""
        redis = await self._get_redis_connection()
        if not redis:
            return {"error": "Redis unavailable"}
        
        try:
            current_time = time.time()
            window_start = current_time - window_seconds
            
            # Clean up expired entries and get count
            pipe = redis.pipeline()
            pipe.zremrangebyscore(key, 0, window_start)
            pipe.zcard(key)
            pipe.ttl(key)
            
            results = await pipe.execute()
            current_count = results[1]
            ttl = results[2]
            
            return {
                "current_count": current_count,
                "window_seconds": window_seconds,
                "ttl": ttl,
                "window_start": window_start,
                "current_time": current_time
            }

        except Exception as e:
            logger.error(f"Failed to get rate limit info for {key}: {e}")
            return {"error": str(e)}


class MemoryRateLimitBackend(RateLimitBackend):
    """In-memory rate limiting fallback."""

    def __init__(self):
        self._cache: OrderedDict[str, list] = OrderedDict()
        self._lock: Optional[asyncio.Lock] = None
        self._last_cleanup = time.time()

    async def _cleanup_expired(self) -> None:
        """Clean up expired entries."""
        current_time = time.time()

        # Only cleanup every few minutes to avoid overhead
        if current_time - self._last_cleanup < rate_limit_config.memory_cleanup_interval:
            return

        self._last_cleanup = current_time
        expired_keys = []

        for key, requests in self._cache.items():
            # Remove expired requests
            self._cache[key] = [req_time for req_time in requests
                              if current_time - req_time < rate_limit_config.window_seconds]

            # Mark empty entries for removal
            if not self._cache[key]:
                expired_keys.append(key)

        # Remove empty entries
        for key in expired_keys:
            del self._cache[key]

        # Enforce max size
        while len(self._cache) > rate_limit_config.memory_max_size:
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]

    async def check_rate_limit(self, key: str, limit: int, window_seconds: int) -> Tuple[bool, int, int]:
        """Check rate limit using in-memory storage."""
        if self._lock is None:
            self._lock = asyncio.Lock()

        async with self._lock:
            await self._cleanup_expired()

            current_time = time.time()
            window_start = current_time - window_seconds

            # Log memory operation for monitoring
            logger.debug(f"💾 Memory rate limit check: key={key}, limit={limit}, window={window_seconds}s")

            # Get or create request list for this key
            if key not in self._cache:
                self._cache[key] = []

            # Remove expired requests
            self._cache[key] = [req_time for req_time in self._cache[key] if req_time > window_start]

            current_count = len(self._cache[key])

            if current_count >= limit:
                # Calculate retry after
                oldest_request = min(self._cache[key]) if self._cache[key] else current_time
                retry_after = max(1, int(oldest_request + window_seconds - current_time))
                logger.info(f"💾 Memory rate limit EXCEEDED: key={key}, count={current_count}/{limit}, retry_after={retry_after}s")
                return False, current_count, retry_after

            # Add current request
            self._cache[key].append(current_time)

            # Move to end for LRU behavior
            self._cache.move_to_end(key)

            logger.debug(f"💾 Memory rate limit OK: key={key}, count={current_count + 1}/{limit}")
            return True, current_count + 1, 0

    async def reset_rate_limit(self, key: str) -> None:
        """Reset rate limit for a key."""
        if self._lock is None:
            self._lock = asyncio.Lock()

        async with self._lock:
            if key in self._cache:
                del self._cache[key]

    async def get_rate_limit_info(self, key: str, window_seconds: int) -> Dict[str, Any]:
        """Get rate limit information."""
        if self._lock is None:
            self._lock = asyncio.Lock()

        async with self._lock:
            current_time = time.time()
            window_start = current_time - window_seconds

            if key not in self._cache:
                return {"current_count": 0, "window_seconds": window_seconds}

            # Count non-expired requests
            valid_requests = [req_time for req_time in self._cache[key] if req_time > window_start]

            return {
                "current_count": len(valid_requests),
                "window_seconds": window_seconds,
                "oldest_request": min(valid_requests) if valid_requests else None,
                "current_time": current_time
            }


class RateLimitService:
    """Rate limiting service with Redis backend and memory fallback."""

    def __init__(self):
        self._redis_backend = RedisRateLimitBackend()
        self._memory_backend = MemoryRateLimitBackend()
        self._backend_lock: Optional[asyncio.Lock] = None
        self._using_memory_fallback = False

    async def _get_backend(self) -> RateLimitBackend:
        """Get the appropriate rate limit backend."""
        if not rate_limit_config.enabled:
            # If rate limiting is disabled, use memory backend with high limits
            logger.debug("🚫 Rate limiting disabled, using memory backend")
            return self._memory_backend

        # Try Redis first
        try:
            await self._redis_backend.check_rate_limit("health_check", 1000, 10)

            # If we were using memory fallback but Redis is now available, log it
            if self._using_memory_fallback:
                logger.info("🔴➡️💾 Switched back to Redis backend for rate limiting")
                self._using_memory_fallback = False

            logger.debug("🔴 Using Redis backend for rate limiting")
            return self._redis_backend

        except Exception as e:
            # If Redis fails and fallback is enabled, use memory backend
            if rate_limit_config.fallback_to_memory:
                if not self._using_memory_fallback:
                    logger.warning(f"🔴❌💾 Redis backend failed, using memory fallback: {e}")
                    self._using_memory_fallback = True
                else:
                    logger.debug("💾 Continuing with memory fallback (Redis still unavailable)")

                return self._memory_backend

            # If fallback is disabled, propagate the error
            raise RateLimitError(f"Rate limiting unavailable: {e}")

    async def check_rate_limit(
        self,
        key: str,
        endpoint_path: str = None,
        limit: int = None,
        window_seconds: int = None
    ) -> Tuple[bool, int, int]:
        """
        Check if rate limit is exceeded.

        Args:
            key: Rate limit key (e.g., IP address)
            endpoint_path: Optional endpoint path for endpoint-specific limits
            limit: Optional custom limit (overrides endpoint-specific limit)
            window_seconds: Optional custom window (overrides global window)

        Returns:
            Tuple of (allowed, current_count, retry_after_seconds)
        """
        if not rate_limit_config.enabled:
            return True, 0, 0

        # Determine limit and window
        if limit is None and endpoint_path:
            limit = rate_limit_config.get_endpoint_limit(endpoint_path)
        elif limit is None:
            limit = rate_limit_config.default_limit

        if window_seconds is None:
            window_seconds = rate_limit_config.window_seconds

        try:
            backend = await self._get_backend()
            return await backend.check_rate_limit(key, limit, window_seconds)

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # On error, allow the request but log the issue
            return True, 0, 0

    async def reset_rate_limit(self, key: str) -> None:
        """Reset rate limit for a key."""
        if not rate_limit_config.enabled:
            return

        try:
            backend = await self._get_backend()
            await backend.reset_rate_limit(key)

        except Exception as e:
            logger.error(f"Failed to reset rate limit for {key}: {e}")

    async def get_rate_limit_info(self, key: str) -> Dict[str, Any]:
        """Get rate limit information."""
        if not rate_limit_config.enabled:
            return {"enabled": False}

        try:
            backend = await self._get_backend()
            info = await backend.get_rate_limit_info(key, rate_limit_config.window_seconds)

            # Add backend type
            info["backend"] = "redis" if backend == self._redis_backend else "memory"
            info["enabled"] = True

            return info

        except Exception as e:
            logger.error(f"Failed to get rate limit info for {key}: {e}")
            return {"error": str(e), "enabled": True}


# Global service instance
rate_limit_service = RateLimitService()
