"""
CSRF Protection Dependencies

FastAPI dependencies for CSRF token generation and validation.
"""

import logging
from typing import Optional
from fastapi import Depends, Request, Response, HTTPException, status

from src.app.core.config.csrf_config import csrf_config
from src.app.core.security.csrf_service import csrf_service, CSRFValidationError

logger = logging.getLogger(__name__)


class CSRFTokenResponse:
    """Response model for CSRF token generation."""
    
    def __init__(self, token: str, expires_in_minutes: int):
        self.token = token
        self.expires_in_minutes = expires_in_minutes
        self.header_name = csrf_config.token_header
        self.cookie_name = csrf_config.cookie_name


async def generate_csrf_token(
    request: Request,
    response: Response
) -> CSRFTokenResponse:
    """
    FastAPI dependency to generate a new CSRF token.
    
    Args:
        request: The HTTP request
        response: The HTTP response (for setting cookies)
        
    Returns:
        CSRFTokenResponse with token details
        
    Usage:
        @app.get("/csrf-token")
        async def get_csrf_token(
            csrf_token: CSRFTokenResponse = Depends(generate_csrf_token)
        ):
            return {
                "csrf_token": csrf_token.token,
                "expires_in_minutes": csrf_token.expires_in_minutes,
                "header_name": csrf_token.header_name
            }
    """
    if not csrf_config.enabled:
        # Return dummy token when CSRF is disabled
        return CSRFTokenResponse(
            token="csrf_disabled",
            expires_in_minutes=0
        )
    
    try:
        # Extract session ID for token binding
        session_id = _extract_session_id(request)
        
        # Generate token
        token = csrf_service.generate_token(session_id)
        
        # Optionally set as cookie (for convenience, but header is preferred)
        if csrf_config.cookie_name:
            from src.app.core.util.auth_utils import AuthUtils
            cookie_settings = AuthUtils.get_cookie_settings()
            
            # Override some settings for CSRF cookie
            csrf_cookie_settings = cookie_settings.copy()
            csrf_cookie_settings['httponly'] = False  # Allow JS access for CSRF token
            csrf_cookie_settings['max_age'] = csrf_config.token_expire_minutes * 60
            
            response.set_cookie(
                key=csrf_config.cookie_name,
                value=token,
                **csrf_cookie_settings
            )
        
        logger.debug(f"Generated CSRF token for session: {session_id}")
        
        return CSRFTokenResponse(
            token=token,
            expires_in_minutes=csrf_config.token_expire_minutes
        )
        
    except Exception as e:
        logger.error(f"Failed to generate CSRF token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate CSRF token"
        )


async def validate_csrf_token_dependency(
    request: Request
) -> bool:
    """
    FastAPI dependency to validate CSRF token.
    
    Args:
        request: The HTTP request
        
    Returns:
        True if token is valid
        
    Raises:
        HTTPException: If token validation fails
        
    Usage:
        @app.post("/protected-endpoint")
        async def protected_endpoint(
            csrf_valid: bool = Depends(validate_csrf_token_dependency),
            payload: MyRequest
        ):
            # This endpoint is now CSRF protected
            return await process_request(payload)
    """
    if not csrf_config.enabled:
        return True
    
    # Skip validation for exempt paths
    if csrf_config.is_path_exempt(request.url.path):
        return True
    
    try:
        # Extract token from various sources
        token = _extract_csrf_token(request)
        
        if not token:
            logger.warning(f"No CSRF token provided for {request.method} {request.url.path}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "CSRF_TOKEN_MISSING",
                    "message": "CSRF token is required",
                    "required_header": csrf_config.token_header,
                    "alternative": "Include csrf_token in JSON body"
                }
            )
        
        # Extract session ID for validation
        session_id = _extract_session_id(request)
        
        # Validate token
        if not csrf_service.validate_token(token, session_id):
            logger.warning(f"Invalid CSRF token for {request.method} {request.url.path}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "CSRF_TOKEN_INVALID",
                    "message": "CSRF token validation failed",
                    "required_header": csrf_config.token_header
                }
            )
        
        return True
        
    except HTTPException:
        raise
    except CSRFValidationError as e:
        logger.error(f"CSRF validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "error": "CSRF_VALIDATION_ERROR",
                "message": "CSRF token validation failed"
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in CSRF validation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during CSRF validation"
        )


def _extract_csrf_token(request: Request) -> Optional[str]:
    """Extract CSRF token from request headers, body, or cookies."""
    # Try header first (most secure)
    token = request.headers.get(csrf_config.token_header)
    if token:
        return token
    
    # Try cookie (less secure, mainly for debugging)
    token = request.cookies.get(csrf_config.cookie_name)
    if token:
        return token
    
    # Note: JSON body extraction is handled by middleware
    # as it requires async body reading
    return None


def _extract_session_id(request: Request) -> Optional[str]:
    """Extract session ID from request for token binding."""
    from src.app.core.config.auth_config import auth_config
    
    # Try session cookie first
    session_id = request.cookies.get(auth_config.session_cookie_name)
    if session_id:
        return session_id
    
    # Try refresh cookie
    session_id = request.cookies.get(auth_config.refresh_cookie_name)
    if session_id:
        return session_id
    
    return None


# Convenience aliases
csrf_token_generator = generate_csrf_token
csrf_token_validator = validate_csrf_token_dependency
