"""
CSRF Protection Service

Provides cryptographically secure CSRF token generation and validation.
"""

import hmac
import hashlib
import secrets
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
from urllib.parse import quote, unquote

from src.app.core.config.csrf_config import csrf_config

logger = logging.getLogger(__name__)


class CSRFError(Exception):
    """Base exception for CSRF-related errors."""
    pass


class CSRFTokenError(CSRFError):
    """Raised when CSRF token operations fail."""
    pass


class CSRFValidationError(CSRFError):
    """Raised when CSRF token validation fails."""
    pass


class CSRFService:
    """
    CSRF protection service providing secure token generation and validation.
    
    Uses HMAC-SHA256 for cryptographically secure token generation and validation.
    Tokens include timestamp for expiration checking.
    """
    
    def __init__(self):
        self._secret_key = csrf_config.secret_key.encode('utf-8')
        self._token_expire_minutes = csrf_config.token_expire_minutes
    
    def generate_token(self, session_id: Optional[str] = None) -> str:
        """
        Generate a cryptographically secure CSRF token.
        
        Args:
            session_id: Optional session identifier for additional binding
            
        Returns:
            Base64-encoded CSRF token
            
        Raises:
            CSRFTokenError: If token generation fails
        """
        try:
            # Generate random nonce
            nonce = secrets.token_urlsafe(32)
            
            # Current timestamp
            timestamp = int(datetime.now(timezone.utc).timestamp())
            
            # Create payload
            payload_parts = [str(timestamp), nonce]
            if session_id:
                payload_parts.append(session_id)
            
            payload = ":".join(payload_parts)
            
            # Generate HMAC signature
            signature = hmac.new(
                self._secret_key,
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Combine payload and signature
            token_data = f"{payload}:{signature}"
            
            # URL-safe base64 encode
            token = quote(token_data, safe='')
            
            logger.debug(f"Generated CSRF token for session: {session_id}")
            return token
            
        except Exception as e:
            logger.error(f"Failed to generate CSRF token: {e}")
            raise CSRFTokenError(f"Token generation failed: {e}")
    
    def validate_token(self, token: str, session_id: Optional[str] = None) -> bool:
        """
        Validate a CSRF token.
        
        Args:
            token: The CSRF token to validate
            session_id: Optional session identifier for binding validation
            
        Returns:
            True if token is valid, False otherwise
            
        Raises:
            CSRFValidationError: If validation process fails
        """
        if not csrf_config.enabled:
            return True
        
        if not token:
            logger.warning("CSRF validation failed: empty token")
            return False
        
        try:
            # URL decode
            token_data = unquote(token)
            
            # Split token parts
            parts = token_data.split(':')
            if len(parts) < 3:  # timestamp:nonce:signature (minimum)
                logger.warning("CSRF validation failed: invalid token format")
                return False
            
            # Extract components
            timestamp_str = parts[0]
            nonce = parts[1]
            provided_signature = parts[-1]  # Last part is always signature
            
            # Reconstruct payload
            payload_parts = [timestamp_str, nonce]
            if session_id and len(parts) == 4:  # timestamp:nonce:session_id:signature
                expected_session = parts[2]
                if expected_session != session_id:
                    logger.warning("CSRF validation failed: session ID mismatch")
                    return False
                payload_parts.append(session_id)
            
            payload = ":".join(payload_parts)
            
            # Verify signature
            expected_signature = hmac.new(
                self._secret_key,
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(provided_signature, expected_signature):
                logger.warning("CSRF validation failed: invalid signature")
                return False
            
            # Check expiration
            try:
                timestamp = int(timestamp_str)
                token_time = datetime.fromtimestamp(timestamp, tz=timezone.utc)
                expiry_time = token_time + timedelta(minutes=self._token_expire_minutes)
                
                if datetime.now(timezone.utc) > expiry_time:
                    logger.warning("CSRF validation failed: token expired")
                    return False
                    
            except (ValueError, OSError) as e:
                logger.warning(f"CSRF validation failed: invalid timestamp: {e}")
                return False
            
            logger.debug(f"CSRF token validated successfully for session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"CSRF validation error: {e}")
            raise CSRFValidationError(f"Token validation failed: {e}")
    
    def get_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Extract information from a CSRF token without full validation.
        
        Args:
            token: The CSRF token to analyze
            
        Returns:
            Dictionary with token information or None if invalid format
        """
        try:
            # URL decode
            token_data = unquote(token)
            
            # Split token parts
            parts = token_data.split(':')
            if len(parts) < 3:
                return None
            
            timestamp_str = parts[0]
            nonce = parts[1]
            
            try:
                timestamp = int(timestamp_str)
                token_time = datetime.fromtimestamp(timestamp, tz=timezone.utc)
                expiry_time = token_time + timedelta(minutes=self._token_expire_minutes)
                is_expired = datetime.now(timezone.utc) > expiry_time
                
                return {
                    'timestamp': timestamp,
                    'created_at': token_time.isoformat(),
                    'expires_at': expiry_time.isoformat(),
                    'is_expired': is_expired,
                    'nonce': nonce[:8] + '...',  # Truncated for security
                    'has_session_binding': len(parts) == 4
                }
                
            except (ValueError, OSError):
                return None
                
        except Exception:
            return None


# Global service instance
csrf_service = CSRFService()
