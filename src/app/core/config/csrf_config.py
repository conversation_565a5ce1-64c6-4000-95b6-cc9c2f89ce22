"""
CSRF Protection Configuration

Provides environment-aware CSRF protection settings with secure defaults.
"""

import logging
from typing import List, Optional

from src.app.core.config.base_environment_config import BaseEnvironmenConfig
from src.app.core.constant.constants import CSRFConstants

logger = logging.getLogger(__name__)


class CSRFConfigError(Exception):
    """Raised when CSRF configuration is invalid."""
    pass


class CSRFConfig(BaseEnvironmenConfig):
    """
    CSRF protection configuration with environment-aware defaults.
    
    Provides secure CSRF token generation and validation settings.
    """
    
    def __init__(self):
        # Core CSRF settings
        self.secret_key = self._get_required_env(CSRFConstants.CSRF_SECRET_KEY.value)
        self.enabled = self._get_bool_env(CSRFConstants.CSRF_ENABLED.value, True)
        
        # Token configuration
        self.token_header = self._get_optional_env(
            CSRFConstants.CSRF_TOKEN_HEADER.value, 
            "X-CSRF-Token"
        )
        self.cookie_name = self._get_optional_env(
            CSRFConstants.CSRF_COOKIE_NAME.value, 
            "csrf_token"
        )
        self.token_expire_minutes = self._get_int_env(
            CSRFConstants.CSRF_TOKEN_EXPIRE_MINUTES.value, 
            60  # 1 hour default
        )
        
        # Exempt paths configuration
        exempt_paths_str = self._get_optional_env(CSRFConstants.CSRF_EXEMPT_PATHS.value, "")
        self.exempt_paths = self._parse_exempt_paths(exempt_paths_str)
        
        self._validate_config()
        self._log_config_loaded("CSRFConfig")
    
    def _parse_exempt_paths(self, exempt_paths_str: str) -> List[str]:
        """Parse comma-separated exempt paths."""
        if not exempt_paths_str:
            return [
                "/auth/login",
                "/auth/callback", 
                "/auth/logout",
                "/docs",
                "/openapi.json",
                "/redoc"
            ]
        
        paths = [path.strip() for path in exempt_paths_str.split(",") if path.strip()]
        return paths
    
    def _validate_config(self) -> None:
        """Validate CSRF configuration."""
        super()._validate_config()
        
        if self.enabled:
            # Validate secret key strength
            if len(self.secret_key) < 32:
                raise CSRFConfigError(
                    f"{CSRFConstants.CSRF_SECRET_KEY.value} must be at least 32 characters long"
                )
            
            # Validate token expiration
            if self.token_expire_minutes <= 0:
                raise CSRFConfigError(
                    f"{CSRFConstants.CSRF_TOKEN_EXPIRE_MINUTES.value} must be positive"
                )
            
            if self.token_expire_minutes > 1440:  # 24 hours
                logger.warning(
                    f"CSRF token expiration is set to {self.token_expire_minutes} minutes. "
                    f"Consider using shorter expiration times for better security."
                )
        
        if self._is_production_environment():
            self._validate_production_config()
    
    def _validate_production_config(self) -> None:
        """Validate production-specific CSRF settings."""
        super()._validate_production_config()
        
        if not self.enabled:
            logger.warning(
                "CSRF protection is disabled in production environment. "
                "This significantly reduces security against cross-site request forgery attacks."
            )
        
        # Validate secret key strength in production
        if self.enabled and len(self.secret_key) < 64:
            logger.warning(
                f"Production environment should use a {CSRFConstants.CSRF_SECRET_KEY.value} "
                f"of at least 64 characters for enhanced security"
            )
        
        # Recommend shorter token expiration in production
        if self.enabled and self.token_expire_minutes > 60:
            logger.warning(
                f"Production environment should use shorter CSRF token expiration "
                f"(current: {self.token_expire_minutes} minutes, recommended: ≤60 minutes)"
            )
    
    def is_path_exempt(self, path: str) -> bool:
        """Check if a path is exempt from CSRF protection."""
        if not self.enabled:
            return True
        
        # Normalize path
        normalized_path = path.rstrip('/')
        
        for exempt_path in self.exempt_paths:
            exempt_normalized = exempt_path.rstrip('/')
            
            # Exact match
            if normalized_path == exempt_normalized:
                return True
            
            # Prefix match for paths ending with /*
            if exempt_normalized.endswith('/*'):
                prefix = exempt_normalized[:-2]
                if normalized_path.startswith(prefix):
                    return True
        
        return False


# Global configuration instance
csrf_config = CSRFConfig()
