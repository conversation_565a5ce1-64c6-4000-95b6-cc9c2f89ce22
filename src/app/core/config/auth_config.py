import logging
from dotenv import load_dotenv

from src.app.core.constant.constants import AuthConfig as AuthConstants, CookieSite, JWTConfig
from src.app.core.config.base_environment_config import BaseEnvironmenConfig, BaseConfigError

load_dotenv()

logger = logging.getLogger(__name__)


class AuthConfigError(BaseConfigError):
    pass


class AuthConfig(BaseEnvironmenConfig):
    def __init__(self):
        self.state_secret = self._get_required_env(AuthConstants.AUTH_STATE_SECRET.value)
        self.nonce_secret = self._get_required_env(AuthConstants.AUTH_NONCE_SECRET.value)

        self.session_cookie_name = self._get_optional_env(AuthConstants.AUTH_SESSION_COOKIE_NAME.value)
        self.refresh_cookie_name = self._get_optional_env(AuthConstants.AUTH_REFRESH_COOKIE_NAME.value)
        self.cookie_domain = self._get_optional_env(AuthConstants.AUTH_COOKIE_DOMAIN.value)

        # Cookie security settings with production-aware defaults
        self.cookie_secure = self._get_cookie_secure_setting()
        self.cookie_samesite = self._get_cookie_samesite_setting()
        self.cookie_max_age = self._get_int_env(AuthConstants.AUTH_COOKIE_MAX_AGE_SECONDS.value)

        self.access_token_expire_minutes = self._get_int_env(JWTConfig.JWT_ACCESS_TOKEN_EXPIRE_MINUTES.value)
        self.refresh_token_expire_hours = self._get_int_env(JWTConfig.JWT_REFRESH_TOKEN_EXPIRE_HOURS.value)

        self._validate_config()
        self._log_config_loaded("AuthConfig")

    def _get_cookie_secure_setting(self) -> bool:
        """Get cookie secure setting with production-aware defaults."""
        if self._is_production_environment():
            # In production, always default to True for security
            return self._get_bool_env(AuthConstants.AUTH_COOKIE_SECURE.value, True)
        else:
            # In development, allow False for local testing
            return self._get_bool_env(AuthConstants.AUTH_COOKIE_SECURE.value, False)

    def _get_cookie_samesite_setting(self) -> str:
        """Get cookie SameSite setting with security-focused defaults."""
        default_samesite = CookieSite.STRICT.value if self._is_production_environment() else CookieSite.LAX.value
        return self._get_optional_env(AuthConstants.AUTH_COOKIE_SAMESITE.value, default_samesite)

    def _validate_config(self) -> None:
        self._validate_secret_strength(self.state_secret, AuthConstants.AUTH_STATE_SECRET.value)
        self._validate_secret_strength(self.nonce_secret, AuthConstants.AUTH_NONCE_SECRET.value)

        if self.cookie_samesite and self.cookie_samesite not in (CookieSite.STRICT.value, CookieSite.LAX.value, CookieSite.NONE.value):
            raise AuthConfigError(f"{AuthConstants.AUTH_COOKIE_SAMESITE.value} must be {CookieSite.STRICT.value}, {CookieSite.LAX.value}, or {CookieSite.NONE.value}")

        self._validate_range(
            self.access_token_expire_minutes,
            JWTConfig.JWT_ACCESS_TOKEN_EXPIRE_MINUTES.value,
            min_value=5,
            max_value=120
        )
        self._validate_range(
            self.refresh_token_expire_hours,
            JWTConfig.JWT_REFRESH_TOKEN_EXPIRE_HOURS.value,
            min_value=1,
            max_value=168
        )

        if self._is_production_environment():
            self._validate_production_config()
    
    def _validate_production_config(self) -> None:
        super()._validate_production_config()

        # Enforce secure cookies in production
        if not self.cookie_secure:
            raise AuthConfigError(
                f"Production environment requires {AuthConstants.AUTH_COOKIE_SECURE.value}=True for secure authentication cookies. "
                f"This is mandatory for HTTPS deployments to prevent cookie interception."
            )

        # Validate SameSite configuration
        if self.cookie_samesite == CookieSite.NONE.value and not self.cookie_secure:
            raise AuthConfigError(
                f"{AuthConstants.AUTH_COOKIE_SAMESITE.value}='{CookieSite.NONE.value}' requires {AuthConstants.AUTH_COOKIE_SECURE.value}=True"
            )

        # Recommend strict SameSite for production
        if self.cookie_samesite not in [CookieSite.STRICT.value, CookieSite.LAX.value]:
            logger.warning(
                f"Production environment should use {AuthConstants.AUTH_COOKIE_SAMESITE.value}='{CookieSite.STRICT.value}' "
                f"or '{CookieSite.LAX.value}' for enhanced CSRF protection. Current: '{self.cookie_samesite}'"
            )

        # Validate cookie domain for production
        if not self.cookie_domain:
            logger.warning(
                f"Production environment should set {AuthConstants.AUTH_COOKIE_DOMAIN.value} "
                f"to restrict cookie scope to your domain"
            )

        self._validate_secret_strength(self.state_secret, AuthConstants.AUTH_STATE_SECRET.value)
        self._validate_secret_strength(self.nonce_secret, AuthConstants.AUTH_NONCE_SECRET.value)


auth_config = AuthConfig()
