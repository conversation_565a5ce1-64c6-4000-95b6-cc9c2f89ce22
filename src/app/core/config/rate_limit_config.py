"""
Rate Limiting Configuration

Configuration for Redis-backed rate limiting with in-memory fallback.
Follows the established configuration pattern in the codebase.
"""

import logging
from typing import Dict, Optional

from src.app.core.config.base_environment_config import BaseEnvironmenConfig
from src.app.core.constant.constants import RateLimitConfig as RateLimitConstants

logger = logging.getLogger(__name__)


class RateLimitConfigError(Exception):
    """Rate limit configuration error."""
    pass


class RateLimitConfig(BaseEnvironmenConfig):
    """Rate limiting configuration with Redis backend and memory fallback."""
    
    def __init__(self):
        # Core settings
        self.enabled = self._get_bool_env(RateLimitConstants.RATE_LIMIT_ENABLED.value, True)
        self.fallback_to_memory = self._get_bool_env(
            RateLimitConstants.RATE_LIMIT_FALLBACK_TO_MEMORY.value, True
        )
        
        # Redis configuration
        self.redis_url = self._get_optional_env(
            RateLimitConstants.RATE_LIMIT_REDIS_URL.value, "redis://localhost:6379"
        )
        self.redis_password = self._get_optional_env(RateLimitConstants.RATE_LIMIT_REDIS_PASSWORD.value)
        self.redis_db = self._get_int_env(RateLimitConstants.RATE_LIMIT_REDIS_DB.value, 0)
        self.redis_max_connections = self._get_int_env(
            RateLimitConstants.RATE_LIMIT_REDIS_MAX_CONNECTIONS.value, 10
        )
        self.redis_ssl = self._get_bool_env(RateLimitConstants.RATE_LIMIT_REDIS_SSL.value, False)
        self.redis_ssl_cert_reqs = self._get_optional_env(
            RateLimitConstants.RATE_LIMIT_REDIS_SSL_CERT_REQS.value, "required"
        )
        
        # Rate limit settings (requests per window)
        self.window_seconds = self._get_int_env(RateLimitConstants.RATE_LIMIT_WINDOW_SECONDS.value, 60)
        
        # Endpoint-specific rate limits
        self.auth_login_limit = self._get_int_env(RateLimitConstants.RATE_LIMIT_AUTH_LOGIN.value, 10)
        self.auth_callback_limit = self._get_int_env(RateLimitConstants.RATE_LIMIT_AUTH_CALLBACK.value, 20)
        self.auth_refresh_limit = self._get_int_env(RateLimitConstants.RATE_LIMIT_AUTH_REFRESH.value, 30)
        self.tenant_create_limit = self._get_int_env(RateLimitConstants.RATE_LIMIT_TENANT_CREATE.value, 5)
        self.email_verify_limit = self._get_int_env(RateLimitConstants.RATE_LIMIT_EMAIL_VERIFY.value, 10)
        self.default_limit = self._get_int_env(RateLimitConstants.RATE_LIMIT_DEFAULT.value, 60)
        
        # Memory fallback configuration
        self.memory_max_size = self._get_int_env(RateLimitConstants.RATE_LIMIT_MEMORY_MAX_SIZE.value, 10000)
        self.memory_cleanup_interval = self._get_int_env(
            RateLimitConstants.RATE_LIMIT_MEMORY_CLEANUP_INTERVAL.value, 300
        )
        
        self._validate_config()
        self._log_config_loaded("RateLimitConfig")
    
    def _validate_config(self) -> None:
        """Validate rate limiting configuration."""
        if not self.enabled:
            logger.info("Rate limiting is disabled")
            return
        
        # Validate Redis URL format
        if self.redis_url and not self.redis_url.startswith(('redis://', 'rediss://')):
            raise RateLimitConfigError("RATE_LIMIT_REDIS_URL must start with redis:// or rediss://")
        
        # Validate rate limits are positive
        rate_limits = {
            'auth_login_limit': self.auth_login_limit,
            'auth_callback_limit': self.auth_callback_limit,
            'auth_refresh_limit': self.auth_refresh_limit,
            'tenant_create_limit': self.tenant_create_limit,
            'email_verify_limit': self.email_verify_limit,
            'default_limit': self.default_limit,
        }
        
        for limit_name, limit_value in rate_limits.items():
            if limit_value <= 0:
                raise RateLimitConfigError(f"{limit_name} must be positive, got: {limit_value}")
        
        # Validate window seconds
        if self.window_seconds <= 0:
            raise RateLimitConfigError(f"window_seconds must be positive, got: {self.window_seconds}")
        
        # Validate memory settings
        if self.memory_max_size <= 0:
            raise RateLimitConfigError(f"memory_max_size must be positive, got: {self.memory_max_size}")
        
        if self.memory_cleanup_interval <= 0:
            raise RateLimitConfigError(
                f"memory_cleanup_interval must be positive, got: {self.memory_cleanup_interval}"
            )
        
        # Validate Redis settings
        if self.redis_db < 0:
            raise RateLimitConfigError(f"redis_db must be non-negative, got: {self.redis_db}")
        
        if self.redis_max_connections <= 0:
            raise RateLimitConfigError(
                f"redis_max_connections must be positive, got: {self.redis_max_connections}"
            )

        # Call production validation if in production
        self._validate_production_config()
    
    def _validate_production_config(self) -> None:
        """Additional validation for production environment."""
        super()._validate_production_config()
        
        if not self.enabled:
            logger.warning("Rate limiting is disabled in production - this is not recommended")
            return
        
        # In production, recommend stricter limits
        if self.auth_login_limit > 20:
            logger.warning(f"auth_login_limit is high for production: {self.auth_login_limit}")
        
        if self.tenant_create_limit > 10:
            logger.warning(f"tenant_create_limit is high for production: {self.tenant_create_limit}")
        
        # Recommend Redis in production
        if self.fallback_to_memory and 'localhost' in self.redis_url:
            logger.warning("Using localhost Redis in production - consider using a dedicated Redis instance")
    
    def get_endpoint_limit(self, endpoint_path: str) -> int:
        """
        Get rate limit for a specific endpoint.
        
        Args:
            endpoint_path: The endpoint path (e.g., '/auth/login')
            
        Returns:
            Rate limit for the endpoint
        """
        endpoint_limits = {
            '/auth/login': self.auth_login_limit,
            '/auth/callback': self.auth_callback_limit,
            '/auth/refresh': self.auth_refresh_limit,
            '/tenant': self.tenant_create_limit,
            '/tenant/verify-email': self.email_verify_limit,
        }
        
        return endpoint_limits.get(endpoint_path, self.default_limit)
    
    def get_redis_connection_params(self) -> Dict:
        """Get Redis connection parameters."""
        params = {
            'url': self.redis_url,
            'db': self.redis_db,
            'max_connections': self.redis_max_connections,
            'decode_responses': True,
            'socket_connect_timeout': 10,
            'socket_timeout': 10,
            'retry_on_timeout': True,
        }

        if self.redis_password:
            params['password'] = self.redis_password

        # SSL configuration
        if self.redis_ssl or self.redis_url.startswith('rediss://'):
            params['ssl'] = True
            if self.redis_ssl_cert_reqs == 'none':
                params['ssl_cert_reqs'] = None

        return params
    
    def is_critical_endpoint(self, endpoint_path: str) -> bool:
        """Check if an endpoint is considered critical for rate limiting."""
        critical_endpoints = {
            '/auth/login',
            '/auth/callback', 
            '/auth/refresh',
            '/tenant',
            '/tenant/verify-email'
        }
        return endpoint_path in critical_endpoints


# Global configuration instance
rate_limit_config = RateLimitConfig()
