"""
CORS Configuration

Provides environment-aware CORS settings with secure defaults for production.
"""

import logging
from typing import List, Optional

from src.app.core.config.base_environment_config import BaseEnvironmenConfig
from src.app.core.constant.constants import CORSConstants

logger = logging.getLogger(__name__)


class CORSConfigError(Exception):
    """Raised when CORS configuration is invalid."""
    pass


class CORSConfig(BaseEnvironmenConfig):
    """
    CORS configuration with environment-aware defaults.
    
    Provides secure CORS settings with strict defaults for production
    and more permissive settings for development.
    """
    
    def __init__(self):
        # Core CORS settings
        self.enabled = self._get_bool_env(CORSConstants.CORS_ENABLED.value, True)
        
        # Origins configuration
        origins_str = self._get_optional_env(CORSConstants.CORS_ALLOW_ORIGINS.value, "")
        self.allow_origins = self._parse_origins(origins_str)
        
        # Methods configuration
        methods_str = self._get_optional_env(CORSConstants.CORS_ALLOW_METHODS.value, "")
        self.allow_methods = self._parse_methods(methods_str)
        
        # Headers configuration
        headers_str = self._get_optional_env(CORSConstants.CORS_ALLOW_HEADERS.value, "")
        self.allow_headers = self._parse_headers(headers_str)
        
        # Credentials and other settings
        self.allow_credentials = self._get_bool_env(CORSConstants.CORS_ALLOW_CREDENTIALS.value, True)
        
        # Expose headers configuration
        expose_headers_str = self._get_optional_env(CORSConstants.CORS_EXPOSE_HEADERS.value, "")
        self.expose_headers = self._parse_expose_headers(expose_headers_str)
        
        # Max age for preflight requests (in seconds)
        self.max_age = self._get_int_env(CORSConstants.CORS_MAX_AGE.value, 86400)  # 24 hours
        
        self._validate_config()
        self._log_config_loaded("CORSConfig")
    
    def _parse_origins(self, origins_str: str) -> List[str]:
        """Parse comma-separated allowed origins."""
        if origins_str is None or origins_str == "":
            if self._is_production_environment():
                # In production, no default origins - must be explicitly set
                return []
            else:
                # Development defaults
                return [
                    "http://localhost:3000",  # React dev server
                    "http://localhost:3001",  # Alternative React port
                    "http://localhost:8080",  # Vue/Angular dev server
                    "http://127.0.0.1:3000",
                    "http://127.0.0.1:3001",
                    "http://127.0.0.1:8080"
                ]

        origins = [origin.strip() for origin in origins_str.split(",") if origin.strip()]
        return origins
    
    def _parse_methods(self, methods_str: str) -> List[str]:
        """Parse comma-separated allowed methods."""
        if methods_str is None or methods_str == "":
            # Secure defaults - only essential methods
            return ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"]

        methods = [method.strip().upper() for method in methods_str.split(",") if method.strip()]
        return methods
    
    def _parse_headers(self, headers_str: str) -> List[str]:
        """Parse comma-separated allowed headers."""
        if headers_str is None or headers_str == "":
            # Secure defaults - only essential headers
            return [
                "Accept",
                "Accept-Language",
                "Content-Language",
                "Content-Type",
                "Authorization",
                "X-CSRF-Token",  # For CSRF protection
                "X-Requested-With"
            ]

        headers = [header.strip() for header in headers_str.split(",") if header.strip()]
        return headers
    
    def _parse_expose_headers(self, expose_headers_str: str) -> List[str]:
        """Parse comma-separated exposed headers."""
        if not expose_headers_str:
            # Default exposed headers
            return [
                "X-Total-Count",  # For pagination
                "X-Rate-Limit-Remaining",  # For rate limiting info
                "X-Rate-Limit-Reset"
            ]
        
        headers = [header.strip() for header in expose_headers_str.split(",") if header.strip()]
        return headers
    
    def _validate_config(self) -> None:
        """Validate CORS configuration."""
        if not self.enabled:
            logger.info("CORS is disabled")
            return
        
        # Validate origins
        if not self.allow_origins:
            if self._is_production_environment():
                raise CORSConfigError(
                    "Production environment requires explicit CORS_ALLOW_ORIGINS configuration. "
                    "Wildcard origins (*) are not allowed in production for security."
                )
            else:
                logger.warning("No CORS origins configured - using development defaults")
        
        # Check for wildcard origins in production
        if self._is_production_environment():
            if "*" in self.allow_origins:
                raise CORSConfigError(
                    "Wildcard origin (*) is not allowed in production environment. "
                    "Please specify explicit frontend domain(s) in CORS_ALLOW_ORIGINS."
                )
        
        # Validate methods
        if not self.allow_methods:
            raise CORSConfigError("CORS_ALLOW_METHODS cannot be empty when CORS is enabled")
        
        # Validate headers
        if not self.allow_headers:
            raise CORSConfigError("CORS_ALLOW_HEADERS cannot be empty when CORS is enabled")
        
        # Validate max age
        if self.max_age < 0:
            raise CORSConfigError("CORS_MAX_AGE must be non-negative")
        
        # Production-specific validations
        if self._is_production_environment():
            self._validate_production_config()
    
    def _validate_production_config(self) -> None:
        """Additional validation for production environment."""
        super()._validate_production_config()
        
        # Ensure origins are HTTPS in production
        for origin in self.allow_origins:
            if origin != "*" and not origin.startswith("https://"):
                logger.warning(
                    f"Production environment should use HTTPS origins. "
                    f"Found HTTP origin: {origin}"
                )
        
        # Warn about overly permissive headers
        dangerous_headers = ["*"]
        for header in self.allow_headers:
            if header in dangerous_headers:
                logger.warning(
                    f"Potentially dangerous CORS header in production: {header}. "
                    f"Consider using explicit header names."
                )
        
        # Recommend shorter max age in production
        if self.max_age > 86400:  # 24 hours
            logger.warning(
                f"Production environment should use shorter CORS max age "
                f"(current: {self.max_age}s, recommended: ≤86400s)"
            )
    
    def is_origin_allowed(self, origin: str) -> bool:
        """Check if an origin is allowed."""
        if not self.enabled:
            return True
        
        if "*" in self.allow_origins:
            return True
        
        return origin in self.allow_origins
    
    def get_config_summary(self) -> dict:
        """Get a summary of the current CORS configuration."""
        base_summary = super().get_config_summary()
        
        cors_summary = {
            "enabled": self.enabled,
            "allow_origins_count": len(self.allow_origins),
            "allow_methods": self.allow_methods,
            "allow_headers_count": len(self.allow_headers),
            "allow_credentials": self.allow_credentials,
            "max_age_hours": self.max_age / 3600
        }
        
        # Don't expose actual origins in logs for security
        if not self._is_production_environment():
            cors_summary["allow_origins"] = self.allow_origins
        
        return {**base_summary, "cors": cors_summary}


# Global configuration instance
try:
    cors_config = CORSConfig()
except Exception as e:
    # In case of configuration errors during import (e.g., in tests),
    # create a minimal config that can be overridden
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Failed to initialize CORS config: {e}. Using minimal config.")

    class MinimalCORSConfig:
        enabled = False
        allow_origins = []
        allow_methods = ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"]
        allow_headers = ["Content-Type", "Authorization"]
        allow_credentials = True
        expose_headers = []
        max_age = 86400

        def is_origin_allowed(self, origin: str) -> bool:
            return False

    cors_config = MinimalCORSConfig()
