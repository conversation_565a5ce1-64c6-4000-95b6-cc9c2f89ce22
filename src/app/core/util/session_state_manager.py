import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Any
from collections import OrderedDict

logger = logging.getLogger(__name__)


class SessionStateError(Exception):
    pass


class SessionStateManager:
    """
    Thread-safe in-memory session state manager for OAuth2 flows.
    
    In production, this should be replaced with Redis or another distributed cache.
    """
    
    def __init__(self, max_size: int = 10000, ttl_minutes: int = 10):
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._max_size = max_size
        self._ttl_minutes = ttl_minutes
        self._lock: Optional[asyncio.Lock] = None

        logger.info(f"Initialized SessionStateManager with max_size={max_size}, ttl_minutes={ttl_minutes}")
    
    async def store_state(
        self, 
        state: str, 
        realm_name: str, 
        tenant_id: str, 
        nonce: str,
        redirect_uri: Optional[str] = None
    ) -> None:
        try:
            if self._lock is None:
                self._lock = asyncio.Lock()

            async with self._lock:
                await self._cleanup_expired()
                
                while len(self._cache) >= self._max_size:
                    oldest_key = next(iter(self._cache))
                    del self._cache[oldest_key]
                    logger.debug(f"Removed oldest state entry: {oldest_key}")
                
                expiry = datetime.now(timezone.utc) + timedelta(minutes=self._ttl_minutes)
                state_data = {
                    'realm_name': realm_name,
                    'tenant_id': tenant_id,
                    'nonce': nonce,
                    'redirect_uri': redirect_uri,
                    'created_at': datetime.now(timezone.utc),
                    'expires_at': expiry
                }
                
                self._cache[state] = state_data
                
                self._cache.move_to_end(state)
                
                logger.debug(f"Stored state for tenant {tenant_id} in realm {realm_name}")
                
        except Exception as e:
            logger.error(f"Error storing state: {e}")
            raise SessionStateError(f"Failed to store state: {e}")
    
    async def retrieve_state(self, state: str) -> Optional[Dict[str, Any]]:
        try:
            if self._lock is None:
                self._lock = asyncio.Lock()

            async with self._lock:
                await self._cleanup_expired()

                state_data = self._cache.get(state)
                if not state_data:
                    logger.warning(f"State not found: {state}")
                    return None

                if datetime.now(timezone.utc) > state_data['expires_at']:
                    del self._cache[state]
                    logger.warning(f"State expired: {state}")
                    return None

                state_copy = state_data.copy()
                del self._cache[state]

                logger.debug(f"Retrieved and removed state for tenant {state_copy.get('tenant_id')}")
                return state_copy

        except Exception as e:
            logger.error(f"Error retrieving state: {e}")
            return None
    
    async def _cleanup_expired(self) -> None:
        try:
            current_time = datetime.now(timezone.utc)
            expired_keys = []
            
            for state, data in self._cache.items():
                if current_time > data['expires_at']:
                    expired_keys.append(state)
            
            for key in expired_keys:
                del self._cache[key]
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired state entries")
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        try:
            if self._lock is None:
                self._lock = asyncio.Lock()

            async with self._lock:
                await self._cleanup_expired()
                
                return {
                    'total_entries': len(self._cache),
                    'max_size': self._max_size,
                    'ttl_minutes': self._ttl_minutes,
                    'oldest_entry_age_seconds': self._get_oldest_entry_age(),
                    'newest_entry_age_seconds': self._get_newest_entry_age()
                }
                
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}
    
    def _get_oldest_entry_age(self) -> Optional[int]:
        if not self._cache:
            return None
        
        oldest_data = next(iter(self._cache.values()))
        age = datetime.now(timezone.utc) - oldest_data['created_at']
        return int(age.total_seconds())
    
    def _get_newest_entry_age(self) -> Optional[int]:
        if not self._cache:
            return None
        
        newest_data = next(reversed(self._cache.values()))
        age = datetime.now(timezone.utc) - newest_data['created_at']
        return int(age.total_seconds())
    
    async def clear_all(self) -> int:
        try:
            if self._lock is None:
                self._lock = asyncio.Lock()

            async with self._lock:
                count = len(self._cache)
                self._cache.clear()
                logger.info(f"Cleared {count} state entries")
                return count
                
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return 0


session_state_manager = SessionStateManager()
