import bcrypt
import secrets
import hashlib
import hmac
import base64
import logging
from datetime import datetime, timezone, timedelta
from typing import <PERSON><PERSON>, Dict, Any, Optional
from urllib.parse import urlencode, parse_qs, urlparse

from src.app.core.config.auth_config import auth_config

logger = logging.getLogger(__name__)


class AuthUtilsError(Exception):
    pass


class AuthUtils:
    
    @staticmethod
    def hash_password(password: str) -> str:
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except Exception:
            return False

    @staticmethod
    def generate_state() -> str:
        try:
            random_state = secrets.token_urlsafe(32)

            signature = hmac.new(
                auth_config.state_secret.encode('utf-8'),
                random_state.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            state_data = f"{random_state}.{signature}"

            encoded_state = base64.urlsafe_b64encode(state_data.encode('utf-8')).decode('utf-8')

            logger.debug("Generated OAuth2 state parameter")
            return encoded_state

        except Exception as e:
            logger.error(f"Failed to generate state: {e}")
            raise AuthUtilsError(f"State generation failed: {e}")

    @staticmethod
    def verify_state(state: str) -> bool:
        try:
            if not state or len(state) > 1000:
                logger.warning("Invalid state: empty or too long")
                return False

            decoded_state = base64.urlsafe_b64decode(state.encode('utf-8')).decode('utf-8')

            parts = decoded_state.split('.')
            if len(parts) != 2:
                logger.warning("Invalid state format")
                return False

            random_state, provided_signature = parts

            if len(provided_signature) != 64:
                logger.warning("Invalid signature length")
                return False

            expected_signature = hmac.new(
                auth_config.state_secret.encode('utf-8'),
                random_state.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            is_valid = hmac.compare_digest(expected_signature, provided_signature)

            if not is_valid:
                logger.warning("State signature verification failed")

            return is_valid

        except Exception as e:
            logger.error(f"State verification failed: {e}")
            return False

    @staticmethod
    def generate_nonce() -> str:
        try:
            random_nonce = secrets.token_urlsafe(32)

            signature = hmac.new(
                auth_config.nonce_secret.encode('utf-8'),
                random_nonce.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            nonce_data = f"{random_nonce}.{signature}"

            encoded_nonce = base64.urlsafe_b64encode(nonce_data.encode('utf-8')).decode('utf-8')

            logger.debug("Generated OAuth2 nonce parameter")
            return encoded_nonce

        except Exception as e:
            logger.error(f"Failed to generate nonce: {e}")
            raise AuthUtilsError(f"Nonce generation failed: {e}")

    @staticmethod
    def verify_nonce(nonce: str) -> bool:
        try:
            if not nonce or len(nonce) > 1000:
                logger.warning("Invalid nonce: empty or too long")
                return False

            decoded_nonce = base64.urlsafe_b64decode(nonce.encode('utf-8')).decode('utf-8')

            parts = decoded_nonce.split('.')
            if len(parts) != 2:
                logger.warning("Invalid nonce format")
                return False

            random_nonce, provided_signature = parts

            if len(provided_signature) != 64:
                logger.warning("Invalid nonce signature length")
                return False

            expected_signature = hmac.new(
                auth_config.nonce_secret.encode('utf-8'),
                random_nonce.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            is_valid = hmac.compare_digest(expected_signature, provided_signature)

            if not is_valid:
                logger.warning("Nonce signature verification failed")

            return is_valid

        except Exception as e:
            logger.error(f"Nonce verification failed: {e}")
            return False

    @staticmethod
    def build_authorization_url(
        base_url: str,
        client_id: str,
        redirect_uri: str,
        state: str,
        nonce: str,
        scope: str = "openid profile email"
    ) -> str:
        try:
            params = {
                'client_id': client_id,
                'response_type': 'code',
                'scope': scope,
                'redirect_uri': redirect_uri,
                'state': state,
                'nonce': nonce
            }

            query_string = urlencode(params)
            authorization_url = f"{base_url}?{query_string}"

            logger.debug(f"Built authorization URL for client {client_id}")
            return authorization_url

        except Exception as e:
            logger.error(f"Failed to build authorization URL: {e}")
            raise AuthUtilsError(f"Authorization URL building failed: {e}")

    @staticmethod
    def extract_tenant_id_from_realm(realm_name: str) -> str:
        try:
            if '-tenant-' in realm_name:
                return realm_name.split('-tenant-')[-1]
            elif realm_name.startswith('tenant-'):
                return realm_name[7:]
            else:
                logger.warning(f"Unexpected realm name format: {realm_name}")
                return realm_name

        except Exception as e:
            logger.error(f"Failed to extract tenant ID from realm {realm_name}: {e}")
            raise AuthUtilsError(f"Tenant ID extraction failed: {e}")

    @staticmethod
    def get_cookie_settings() -> Dict[str, Any]:
        """
        Get secure cookie settings for authentication cookies.

        Always enforces HttpOnly=True for security.
        In production, validates that Secure=True is set.

        Returns:
            Dict containing cookie security settings

        Raises:
            AuthUtilsError: If production security requirements are not met
        """
        cookie_settings = {
            'domain': auth_config.cookie_domain,
            'secure': auth_config.cookie_secure,
            'httponly': True,  # Always enforce HttpOnly for authentication cookies
            'samesite': auth_config.cookie_samesite,
            'max_age': auth_config.cookie_max_age
        }

        # Additional production validation
        if auth_config._is_production_environment():
            if not cookie_settings['secure']:
                raise AuthUtilsError(
                    "Production environment requires secure=True for authentication cookies. "
                    "Ensure HTTPS is configured and AUTH_COOKIE_SECURE=true is set."
                )

            if not cookie_settings['samesite'] or cookie_settings['samesite'] == 'none':
                logger.warning(
                    "Production environment should use SameSite=strict or SameSite=lax "
                    "for enhanced CSRF protection"
                )

        return cookie_settings

