#!/usr/bin/env python3
"""
Simple test runner for CSRF functionality without requiring pytest.
"""

import os
import sys
import traceback
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up test environment variables
os.environ.update({
    'CSRF_SECRET_KEY': 'test_secret_key_that_is_long_enough_for_security_requirements_and_testing',
    'CSRF_ENABLED': 'true',
    'CSRF_TOKEN_HEADER': 'X-CSRF-Token',
    'CSRF_COOKIE_NAME': 'csrf_token',
    'CSRF_TOKEN_EXPIRE_MINUTES': '60',
    'CSRF_EXEMPT_PATHS': '/auth/login,/auth/callback,/docs',
    'ENVIRONMENT': 'testing',
    'AUTH_SESSION_COOKIE_NAME': 'session_token',
    'AUTH_REFRESH_COOKIE_NAME': 'refresh_token'
})

def run_csrf_service_tests():
    """Run CSRF service tests manually."""
    print("🔒 Running CSRF Service Tests")
    print("=" * 40)
    
    try:
        from src.tests.test_csrf_service_simple import TestCSRFService

        test_instance = TestCSRFService()
        test_instance.setup_method()
        
        tests = [
            ('test_generate_token_success', test_instance.test_generate_token_success),
            ('test_generate_token_with_session_id', test_instance.test_generate_token_with_session_id),
            ('test_validate_token_success', test_instance.test_validate_token_success),
            ('test_validate_token_with_session_id', test_instance.test_validate_token_with_session_id),
            ('test_validate_empty_token', test_instance.test_validate_empty_token),
            ('test_validate_invalid_token_format', test_instance.test_validate_invalid_token_format),
            ('test_validate_token_invalid_signature', test_instance.test_validate_token_invalid_signature),
            ('test_validate_token_when_disabled', test_instance.test_validate_token_when_disabled),
            ('test_get_token_info_success', test_instance.test_get_token_info_success),
            ('test_get_token_info_with_session', test_instance.test_get_token_info_with_session),
            ('test_get_token_info_invalid_token', test_instance.test_get_token_info_invalid_token),
            ('test_token_uniqueness', test_instance.test_token_uniqueness),
            ('test_token_security', test_instance.test_token_security),
            ('test_token_generation_error_handling', test_instance.test_token_generation_error_handling),
            ('test_token_validation_error_handling', test_instance.test_token_validation_error_handling),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"Running {test_name}...", end=" ")
                test_func()
                print("✅ PASSED")
                passed += 1
            except Exception as e:
                print(f"❌ FAILED: {e}")
                failed += 1
        
        print("\n" + "=" * 40)
        print(f"CSRF Service Tests: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        print(f"❌ Failed to run CSRF service tests: {e}")
        traceback.print_exc()
        return False

def run_csrf_config_tests():
    """Run CSRF configuration tests."""
    print("\n🔧 Running CSRF Configuration Tests")
    print("=" * 40)
    
    try:
        from src.app.core.config.csrf_config import csrf_config
        
        tests = [
            ("Configuration loading", lambda: csrf_config.enabled == True),
            ("Secret key set", lambda: len(csrf_config.secret_key) >= 32),
            ("Token header configured", lambda: csrf_config.token_header == 'X-CSRF-Token'),
            ("Cookie name configured", lambda: csrf_config.cookie_name == 'csrf_token'),
            ("Token expiration set", lambda: csrf_config.token_expire_minutes == 60),
            ("Exempt paths configured", lambda: '/auth/login' in csrf_config.exempt_paths),
            ("Path exemption works", lambda: csrf_config.is_path_exempt('/auth/login')),
            ("Non-exempt path detected", lambda: not csrf_config.is_path_exempt('/api/users')),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"Testing {test_name}...", end=" ")
                result = test_func()
                if result:
                    print("✅ PASSED")
                    passed += 1
                else:
                    print("❌ FAILED")
                    failed += 1
            except Exception as e:
                print(f"❌ FAILED: {e}")
                failed += 1
        
        print("\n" + "=" * 40)
        print(f"CSRF Configuration Tests: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        print(f"❌ Failed to run CSRF configuration tests: {e}")
        traceback.print_exc()
        return False

def run_csrf_integration_tests():
    """Run basic CSRF integration tests."""
    print("\n🔗 Running CSRF Integration Tests")
    print("=" * 40)
    
    try:
        from src.app.core.security.csrf_service import csrf_service
        from src.app.core.config.csrf_config import csrf_config
        
        tests = [
            ("Service instantiation", lambda: csrf_service is not None),
            ("Token generation", lambda: len(csrf_service.generate_token()) > 0),
            ("Token validation", lambda: csrf_service.validate_token(csrf_service.generate_token())),
            ("Invalid token rejection", lambda: not csrf_service.validate_token("invalid")),
            ("Session binding", lambda: csrf_service.validate_token(
                csrf_service.generate_token("session123"), "session123")),
            ("Session mismatch rejection", lambda: not csrf_service.validate_token(
                csrf_service.generate_token("session123"), "wrong_session")),
            ("Token info extraction", lambda: csrf_service.get_token_info(
                csrf_service.generate_token()) is not None),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"Testing {test_name}...", end=" ")
                result = test_func()
                if result:
                    print("✅ PASSED")
                    passed += 1
                else:
                    print("❌ FAILED")
                    failed += 1
            except Exception as e:
                print(f"❌ FAILED: {e}")
                failed += 1
        
        print("\n" + "=" * 40)
        print(f"CSRF Integration Tests: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        print(f"❌ Failed to run CSRF integration tests: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all CSRF tests."""
    print("🛡️  CSRF Protection Test Suite")
    print("=" * 50)
    
    all_passed = True
    
    # Run configuration tests
    if not run_csrf_config_tests():
        all_passed = False
    
    # Run service tests
    if not run_csrf_service_tests():
        all_passed = False
    
    # Run integration tests
    if not run_csrf_integration_tests():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All CSRF tests passed!")
        print("\n✅ CSRF protection is working correctly")
        print("✅ Configuration is valid")
        print("✅ Token generation and validation working")
        print("✅ Security features functioning")
        return 0
    else:
        print("⚠️  Some CSRF tests failed")
        print("\n❌ Please check the implementation")
        return 1

if __name__ == "__main__":
    sys.exit(main())
