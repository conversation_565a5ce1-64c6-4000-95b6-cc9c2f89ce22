# CSRF Protection Implementation

This document describes the comprehensive CSRF (Cross-Site Request Forgery) protection implementation in SecuriNest, providing robust defense against cross-site request forgery attacks.

## Overview

The CSRF protection system implements multiple layers of security:
- **Cryptographically secure token generation** using HMAC-SHA256
- **Automatic middleware validation** for state-changing requests
- **Session binding** for enhanced security
- **Flexible token delivery** via headers, JSON body, or cookies
- **Configurable exemptions** for authentication endpoints

## Architecture

### Components

1. **CSRFConfig** (`src/app/core/config/csrf_config.py`)
   - Environment-aware configuration
   - Secure defaults for production
   - Path exemption management

2. **CSRFService** (`src/app/core/security/csrf_service.py`)
   - Token generation and validation
   - HMAC-SHA256 cryptographic operations
   - Session binding support

3. **CSRFMiddleware** (`src/app/core/middleware/csrf_middleware.py`)
   - Automatic request validation
   - Multiple token source support
   - Error handling and logging

4. **CSRF Dependencies** (`src/app/core/security/csrf_dependencies.py`)
   - FastAPI dependency injection
   - Token generation endpoints
   - Manual validation support

5. **CSRF Router** (`src/app/api/router/csrf_router.py`)
   - Token generation API
   - Configuration information
   - Testing endpoints

## Configuration

### Environment Variables

```bash
# Core CSRF settings
CSRF_SECRET_KEY=your_64_character_secret_key_for_production_security
CSRF_ENABLED=true

# Token configuration
CSRF_TOKEN_HEADER=X-CSRF-Token
CSRF_COOKIE_NAME=csrf_token
CSRF_TOKEN_EXPIRE_MINUTES=60

# Exempt paths (comma-separated)
CSRF_EXEMPT_PATHS=/auth/login,/auth/callback,/auth/logout,/docs,/openapi.json
```

### Production Requirements

- **Secret Key**: Minimum 64 characters for production
- **Token Expiration**: Recommended ≤60 minutes
- **HTTPS Required**: For secure cookie transmission
- **Logging**: Monitor CSRF validation failures

## Usage

### 1. Obtaining CSRF Tokens

#### Via API Endpoint
```bash
curl -X GET /csrf/token
```

Response:
```json
{
  "csrf_token": "encoded_token_here",
  "expires_in_minutes": 60,
  "usage": {
    "header_name": "X-CSRF-Token",
    "header_example": "X-CSRF-Token: encoded_token_here",
    "json_body_example": {
      "csrf_token": "encoded_token_here",
      "your_data": "..."
    }
  }
}
```

#### Via FastAPI Dependency
```python
from src.app.core.security.csrf_dependencies import generate_csrf_token

@app.get("/my-endpoint")
async def my_endpoint(
    csrf_token: CSRFTokenResponse = Depends(generate_csrf_token)
):
    return {"csrf_token": csrf_token.token}
```

### 2. Using CSRF Tokens

#### Method 1: HTTP Header (Recommended)
```bash
curl -X POST /api/users \
  -H "X-CSRF-Token: your_token_here" \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe"}'
```

#### Method 2: JSON Body
```bash
curl -X POST /api/users \
  -H "Content-Type: application/json" \
  -d '{
    "csrf_token": "your_token_here",
    "name": "John Doe"
  }'
```

#### Method 3: Cookie (Less Secure)
```javascript
// Token automatically included if set as cookie
fetch('/api/users', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({name: 'John Doe'})
});
```

### 3. Frontend Integration

#### JavaScript/React Example
```javascript
class CSRFService {
  constructor() {
    this.token = null;
    this.tokenExpiry = null;
  }

  async getToken() {
    if (this.token && this.tokenExpiry > Date.now()) {
      return this.token;
    }

    const response = await fetch('/csrf/token');
    const data = await response.json();
    
    this.token = data.csrf_token;
    this.tokenExpiry = Date.now() + (data.expires_in_minutes * 60 * 1000);
    
    return this.token;
  }

  async makeProtectedRequest(url, options = {}) {
    const token = await this.getToken();
    
    return fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'X-CSRF-Token': token,
        'Content-Type': 'application/json'
      }
    });
  }
}

// Usage
const csrfService = new CSRFService();

// Make protected request
const response = await csrfService.makeProtectedRequest('/api/users', {
  method: 'POST',
  body: JSON.stringify({name: 'John Doe'})
});
```

## Security Features

### 1. Cryptographic Security
- **HMAC-SHA256**: Cryptographically secure token generation
- **Random Nonces**: Prevent token prediction
- **Timestamp Validation**: Automatic expiration
- **Signature Verification**: Tamper detection

### 2. Session Binding
- Tokens bound to authenticated sessions when available
- Prevents token reuse across different sessions
- Enhanced security for authenticated users

### 3. Multiple Token Sources
- **Priority Order**: Header → JSON Body → Cookie
- **Flexibility**: Supports different client architectures
- **Security Levels**: Headers most secure, cookies least secure

### 4. Automatic Protection
- **Middleware Integration**: Transparent protection
- **Method Filtering**: Only state-changing methods protected
- **Path Exemptions**: Authentication endpoints excluded
- **Error Handling**: Consistent error responses

## Protected Methods

CSRF protection applies to these HTTP methods:
- `POST` - Create operations
- `PUT` - Update operations  
- `PATCH` - Partial updates
- `DELETE` - Delete operations

Safe methods (`GET`, `HEAD`, `OPTIONS`) are not protected.

## Exempt Paths

Default exempt paths:
- `/auth/login` - Authentication endpoint
- `/auth/callback` - OAuth callback
- `/auth/logout` - Logout endpoint
- `/docs` - API documentation
- `/openapi.json` - OpenAPI specification
- `/redoc` - ReDoc documentation

## Error Responses

### Missing Token
```json
{
  "error": "CSRF_TOKEN_INVALID",
  "message": "CSRF token validation failed",
  "details": {
    "required_header": "X-CSRF-Token",
    "alternative": "Include csrf_token in JSON body",
    "documentation": "See API documentation for CSRF protection details"
  }
}
```

### Invalid Token
```json
{
  "error": "CSRF_TOKEN_INVALID", 
  "message": "CSRF token validation failed",
  "details": {
    "required_header": "X-CSRF-Token"
  }
}
```

## Testing

### Unit Tests
```bash
# Run CSRF-specific tests
pytest src/tests/test_csrf_service.py -v
pytest src/tests/test_csrf_middleware.py -v
```

### Manual Testing
```bash
# Get token
TOKEN=$(curl -s /csrf/token | jq -r '.csrf_token')

# Test protected endpoint
curl -X POST /api/users \
  -H "X-CSRF-Token: $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "Test User"}'
```

## Monitoring and Logging

### Log Messages
- Token generation: `DEBUG` level
- Validation success: `DEBUG` level  
- Validation failures: `WARNING` level
- Configuration issues: `WARNING` level
- Errors: `ERROR` level

### Metrics to Monitor
- CSRF validation failure rate
- Token generation frequency
- Exempt path usage
- Error response patterns

## Best Practices

### For Developers
1. **Always use headers** for token transmission when possible
2. **Implement token refresh** logic in frontend applications
3. **Handle CSRF errors** gracefully with user-friendly messages
4. **Test CSRF protection** in your endpoint tests

### For Operations
1. **Monitor CSRF failures** for potential attacks
2. **Use strong secret keys** (64+ characters in production)
3. **Set appropriate token expiration** (≤60 minutes recommended)
4. **Enable HTTPS** for secure token transmission

### For Security
1. **Regular secret rotation** for CSRF keys
2. **Log analysis** for attack pattern detection
3. **Rate limiting** on token generation endpoints
4. **Security headers** for additional protection

## Troubleshooting

### Common Issues

1. **Token Expired**
   - Solution: Implement automatic token refresh
   - Prevention: Use shorter-lived tokens

2. **Missing Token**
   - Solution: Ensure frontend includes token in requests
   - Check: Verify token generation endpoint works

3. **Invalid Token Format**
   - Solution: Check token encoding/decoding
   - Verify: Token transmission method

4. **Session Binding Issues**
   - Solution: Ensure consistent session cookies
   - Check: Authentication state

### Debug Endpoints

```bash
# Check CSRF configuration
curl /csrf/info

# Validate specific token
curl -X POST /csrf/validate \
  -H "Content-Type: application/json" \
  -d '{"csrf_token": "your_token_here"}'
```
