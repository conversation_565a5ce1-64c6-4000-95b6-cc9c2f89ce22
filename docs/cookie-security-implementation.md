# Cookie Security Implementation

This document describes the comprehensive cookie security implementation in SecuriNest, ensuring that authentication cookies are properly secured in production environments.

## Overview

The implementation enforces the following security flags on authentication cookies:
- **HttpOnly=true**: Always enforced to prevent XSS attacks
- **Secure=true**: Enforced in production to prevent HTTP transmission
- **SameSite=strict/lax**: Configurable with secure defaults to prevent CSRF attacks

## Implementation Details

### 1. Configuration Layer (`src/app/core/config/auth_config.py`)

The `AuthConfig` class provides environment-aware cookie security settings:

```python
class AuthConfig(BaseEnvironmenConfig):
    def __init__(self):
        # Cookie security settings with production-aware defaults
        self.cookie_secure = self._get_cookie_secure_setting()
        self.cookie_samesite = self._get_cookie_samesite_setting()
        
    def _get_cookie_secure_setting(self) -> bool:
        """Get cookie secure setting with production-aware defaults."""
        if self._is_production_environment():
            # In production, always default to True for security
            return self._get_bool_env(AuthConstants.AUTH_COOKIE_SECURE.value, True)
        else:
            # In development, allow False for local testing
            return self._get_bool_env(AuthConstants.AUTH_COOKIE_SECURE.value, False)
    
    def _get_cookie_samesite_setting(self) -> str:
        """Get cookie SameSite setting with security-focused defaults."""
        default_samesite = CookieSite.STRICT.value if self._is_production_environment() else CookieSite.LAX.value
        return self._get_optional_env(AuthConstants.AUTH_COOKIE_SAMESITE.value, default_samesite)
```

### 2. Production Validation

The configuration enforces strict security requirements in production:

```python
def _validate_production_config(self) -> None:
    # Enforce secure cookies in production
    if not self.cookie_secure:
        raise AuthConfigError(
            f"Production environment requires {AuthConstants.AUTH_COOKIE_SECURE.value}=True "
            f"for secure authentication cookies. This is mandatory for HTTPS deployments."
        )
    
    # Validate SameSite configuration
    if self.cookie_samesite == CookieSite.NONE.value and not self.cookie_secure:
        raise AuthConfigError(
            f"{AuthConstants.AUTH_COOKIE_SAMESITE.value}='none' requires "
            f"{AuthConstants.AUTH_COOKIE_SECURE.value}=True"
        )
```

### 3. Cookie Settings Utility (`src/app/core/util/auth_utils.py`)

The `AuthUtils.get_cookie_settings()` method provides secure cookie settings:

```python
@staticmethod
def get_cookie_settings() -> Dict[str, Any]:
    """Get secure cookie settings for authentication cookies."""
    cookie_settings = {
        'domain': auth_config.cookie_domain,
        'secure': auth_config.cookie_secure,
        'httponly': True,  # Always enforce HttpOnly for authentication cookies
        'samesite': auth_config.cookie_samesite,
        'max_age': auth_config.cookie_max_age
    }
    
    # Additional production validation
    if auth_config._is_production_environment():
        if not cookie_settings['secure']:
            raise AuthUtilsError(
                "Production environment requires secure=True for authentication cookies. "
                "Ensure HTTPS is configured and AUTH_COOKIE_SECURE=true is set."
            )
    
    return cookie_settings
```

### 4. Usage in Authentication Service

The authentication service uses these secure settings when setting cookies:

```python
# In callback handler
cookie_settings = AuthUtils.get_cookie_settings()
response.set_cookie(
    key=auth_config.refresh_cookie_name,
    value=token_data['refresh_token'],
    **cookie_settings  # Includes all security flags
)

# In logout handler
response.delete_cookie(
    key=auth_config.refresh_cookie_name,
    domain=cookie_settings.get('domain'),
    secure=cookie_settings.get('secure'),
    httponly=True,
    samesite=cookie_settings.get('samesite')
)
```

## Environment Configuration

### Development Environment (.env)
```bash
ENVIRONMENT=development
AUTH_COOKIE_SECURE=false      # Allowed for local testing
AUTH_COOKIE_SAMESITE=lax       # Default for development
AUTH_COOKIE_DOMAIN=localhost
```

### Production Environment
```bash
ENVIRONMENT=production
AUTH_COOKIE_SECURE=true        # ENFORCED - will fail if false
AUTH_COOKIE_SAMESITE=strict    # RECOMMENDED (or lax minimum)
AUTH_COOKIE_DOMAIN=yourdomain.com
```

## Security Features

### 1. Always Enforced
- **HttpOnly=true**: Prevents JavaScript access to cookies (XSS protection)
- **Max-Age**: Configurable session timeout

### 2. Production Enforced
- **Secure=true**: Prevents transmission over HTTP (requires HTTPS)
- **SameSite**: Defaults to 'strict' in production for CSRF protection

### 3. Validation & Warnings
- Configuration validation at startup
- Runtime validation when setting cookies
- Warnings for suboptimal security settings

## Testing

The implementation includes comprehensive tests:

```python
class TestCookieSecurity:
    def test_cookie_settings_production_environment_secure(self):
        """Test secure cookie settings in production."""
        # Production with secure=True should work
        
    def test_cookie_settings_production_environment_insecure_raises_error(self):
        """Test that insecure cookies in production raise an error."""
        # Production with secure=False should fail
        
    def test_cookie_settings_development_environment(self):
        """Test cookie settings in development environment."""
        # Development allows insecure cookies for local testing
```

## Security Benefits

1. **XSS Protection**: HttpOnly flag prevents JavaScript access to authentication cookies
2. **MITM Protection**: Secure flag ensures cookies are only sent over HTTPS in production
3. **CSRF Protection**: SameSite flag prevents cross-site request forgery attacks
4. **Domain Scoping**: Domain restriction limits cookie scope to your application
5. **Session Control**: Max-Age provides session timeout control

## Migration Guide

### For Existing Deployments

1. **Ensure HTTPS is configured** before enabling secure cookies
2. **Set environment variables**:
   ```bash
   ENVIRONMENT=production
   AUTH_COOKIE_SECURE=true
   AUTH_COOKIE_SAMESITE=strict
   AUTH_COOKIE_DOMAIN=yourdomain.com
   ```
3. **Test thoroughly** in staging environment first
4. **Monitor logs** for any cookie-related warnings or errors

### For New Deployments

The secure defaults will be automatically applied when `ENVIRONMENT=production` is set.

## Troubleshooting

### Common Issues

1. **"Production environment requires secure=True"**
   - Ensure HTTPS is properly configured
   - Set `AUTH_COOKIE_SECURE=true` in environment variables

2. **Cookies not being set in production**
   - Verify HTTPS is working correctly
   - Check that domain matches your actual domain
   - Ensure SameSite policy is compatible with your frontend

3. **CSRF issues with SameSite=strict**
   - Consider using `AUTH_COOKIE_SAMESITE=lax` if needed
   - Ensure your frontend handles CSRF tokens properly

## Compliance

This implementation helps meet security compliance requirements:
- **OWASP**: Follows OWASP cookie security guidelines
- **GDPR**: Proper session management and data protection
- **SOC 2**: Secure authentication and session handling
- **PCI DSS**: Secure transmission and storage of authentication data
