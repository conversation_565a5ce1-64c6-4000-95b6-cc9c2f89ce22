# Rate Limiting

SecuriNest implements comprehensive rate limiting to protect critical endpoints from brute force attacks, denial of service, and other abuse. This document explains the rate limiting implementation, configuration, and best practices.

## Overview

The rate limiting system uses a Redis-backed sliding window algorithm with in-memory fallback. It protects critical endpoints like authentication, tenant creation, and email verification while allowing normal API usage.

### Protected Endpoints

The following critical endpoints are protected by default:

| Endpoint | Default Limit | Description |
|----------|---------------|-------------|
| `/auth/login` | 10 per minute | OAuth2 login initiation |
| `/auth/callback` | 20 per minute | OAuth2 callback processing |
| `/auth/refresh` | 30 per minute | Token refresh |
| `/tenant` (POST) | 5 per minute | Tenant creation |
| `/tenant/verify-email` | 10 per minute | Email verification |

All other endpoints use a higher default limit (60 per minute).

## Architecture

The rate limiting implementation consists of several components:

1. **RateLimitMiddleware**: FastAPI middleware that intercepts requests and applies rate limiting
2. **RateLimitService**: Core service that manages rate limit checking and backend selection
3. **RateLimitBackend**: Abstract interface for rate limiting backends
   - **RedisRateLimitBackend**: Primary backend using Redis for distributed rate limiting
   - **MemoryRateLimitBackend**: Fallback backend using in-memory storage

### Rate Limiting Algorithm

SecuriNest uses a sliding window algorithm for accurate rate limiting:

1. Each request increments a counter for the current time window
2. Expired entries are automatically removed from the window
3. If the counter exceeds the limit, the request is rejected with a 429 status code
4. The `Retry-After` header indicates when the client can retry

This approach provides more accurate rate limiting than fixed windows while being efficient in terms of memory and computation.

## Configuration

Rate limiting is configured through environment variables:

```
# Enable/disable rate limiting
RATE_LIMIT_ENABLED=true

# Redis configuration
RATE_LIMIT_REDIS_URL=redis://localhost:6379
RATE_LIMIT_REDIS_PASSWORD=
RATE_LIMIT_REDIS_DB=0
RATE_LIMIT_REDIS_MAX_CONNECTIONS=10

# Fallback to in-memory rate limiting if Redis unavailable
RATE_LIMIT_FALLBACK_TO_MEMORY=true

# Rate limits (requests per minute per IP)
RATE_LIMIT_AUTH_LOGIN=10
RATE_LIMIT_AUTH_CALLBACK=20
RATE_LIMIT_AUTH_REFRESH=30
RATE_LIMIT_TENANT_CREATE=5
RATE_LIMIT_EMAIL_VERIFY=10
RATE_LIMIT_DEFAULT=60

# Rate limit window in seconds (60 = 1 minute)
RATE_LIMIT_WINDOW_SECONDS=60

# Memory fallback configuration
RATE_LIMIT_MEMORY_MAX_SIZE=10000
RATE_LIMIT_MEMORY_CLEANUP_INTERVAL=300
```

### Production Recommendations

For production environments, we recommend:

1. **Use Redis**: Configure a dedicated Redis instance for rate limiting
2. **Stricter Limits**: Consider lowering limits for critical endpoints
3. **Keep Fallback Enabled**: Enable memory fallback for resilience
4. **Monitor Rate Limiting**: Set up alerts for excessive rate limit hits

## Client Experience

When a client exceeds the rate limit, they receive a 429 Too Many Requests response with:

```json
{
  "detail": "Rate limit exceeded. Please try again later.",
  "error": "too_many_requests",
  "retry_after": 30
}
```

The response includes standard rate limiting headers:

- `Retry-After`: Seconds until the client can retry
- `X-RateLimit-Limit`: Maximum requests allowed in the window
- `X-RateLimit-Remaining`: Remaining requests in the current window
- `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## Implementation Details

### IP Address Extraction

The rate limiting system extracts client IP addresses from:

1. `X-Forwarded-For` header (for clients behind proxies)
2. Direct client connection information

This allows rate limiting to work correctly in various deployment scenarios, including behind load balancers and proxies.

### Path Normalization

To prevent rate limit bypassing, the system normalizes paths by removing path parameters:

- `/tenant/123` → `/tenant/{id}`
- `/auth/custom-endpoint` → `/auth/{endpoint}`

This ensures that clients can't bypass rate limits by using different path parameters.

## Testing

You can test rate limiting functionality using the provided script:

```bash
python scripts/test_rate_limiting.py
```

This script makes multiple requests to critical endpoints and verifies that rate limiting is working correctly.

## Monitoring

Rate limiting events are logged with the following information:

- Client IP address
- Endpoint path
- Current request count
- Rate limit threshold
- Retry-after period

Example log message:
```
WARNING:src.app.core.middleware.rate_limit_middleware:Rate limit exceeded for /auth/login from ************* (11 requests, retry after 30s)
```

## Troubleshooting

### Rate Limiting Not Working

If rate limiting doesn't seem to be working:

1. Check that `RATE_LIMIT_ENABLED` is set to `true`
2. Verify Redis connection if using Redis backend
3. Check that the limits are set appropriately
4. Look for errors in the application logs

### Redis Connection Issues

If Redis connection fails:

1. The system will automatically fall back to in-memory rate limiting if enabled
2. Check Redis connection parameters
3. Verify that Redis is running and accessible
4. Check for network issues between the application and Redis

## Future Improvements

Planned improvements to the rate limiting system:

1. **User-Based Limits**: Add rate limiting based on authenticated user IDs
2. **Tenant-Based Limits**: Add rate limiting based on tenant IDs
3. **Dynamic Limits**: Allow adjusting rate limits based on load or time of day
4. **Rate Limit Dashboard**: Add a dashboard for monitoring rate limiting metrics
