# CORS Implementation

This document describes the Cross-Origin Resource Sharing (CORS) implementation in the SecuriNest backend application.

## Overview

The CORS implementation provides secure cross-origin request handling with environment-aware defaults and comprehensive security controls. It follows the same configuration patterns as other security features in the application.

## Architecture

The CORS implementation consists of several components:

1. **CORSConfig** (`src/app/core/config/cors_config.py`)
   - Environment-aware configuration
   - Secure defaults for production
   - Origin, method, and header validation

2. **CORSMiddleware** (`src/app/core/middleware/cors_middleware.py`)
   - Request/response processing
   - Preflight request handling
   - Header management

3. **Constants** (`src/app/core/constant/constants.py`)
   - Environment variable definitions
   - Configuration keys

## Configuration

### Environment Variables

```bash
# Enable/disable CORS protection
CORS_ENABLED=true

# Allowed origins (comma-separated, no spaces)
CORS_ALLOW_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com

# Allowed HTTP methods (comma-separated, no spaces)
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS

# Allowed headers (comma-separated, no spaces)
CORS_ALLOW_HEADERS=Accept,Accept-Language,Content-Language,Content-Type,Authorization,X-CSRF-Token,X-Requested-With

# Allow credentials (cookies, authorization headers)
CORS_ALLOW_CREDENTIALS=true

# Headers to expose to the client (comma-separated, no spaces)
CORS_EXPOSE_HEADERS=X-Total-Count,X-Rate-Limit-Remaining,X-Rate-Limit-Reset

# Max age for preflight requests in seconds
CORS_MAX_AGE=86400
```

### Environment-Aware Defaults

#### Development Environment
- **Origins**: Includes common development ports (localhost:3000, 3001, 8080)
- **Security**: More permissive for easier development
- **Wildcards**: Allowed with appropriate warnings

#### Production Environment
- **Origins**: Must be explicitly configured (no defaults)
- **Security**: Strict validation and HTTPS enforcement
- **Wildcards**: Explicitly forbidden for security

## Security Features

### Origin Validation
- Explicit allow-list of trusted domains
- No wildcard origins in production
- HTTPS enforcement in production environments

### Method Restrictions
- Only essential HTTP methods allowed by default
- Configurable method restrictions
- Automatic OPTIONS handling for preflight

### Header Controls
- Explicit allow-list of request headers
- Essential headers included by default (Authorization, Content-Type, X-CSRF-Token)
- Configurable exposed response headers

### Credentials Handling
- Secure credential handling with origin validation
- No wildcard origins when credentials are enabled
- Production-aware defaults

## Integration with Other Security Features

### CSRF Protection
- X-CSRF-Token header included in default allowed headers
- Seamless integration with CSRF middleware
- Proper preflight handling for CSRF-protected endpoints

### Rate Limiting
- CORS middleware positioned before rate limiting
- Rate limit headers exposed by default
- Proper handling of preflight requests

### Authentication
- Authorization header included in defaults
- Credential support for authenticated requests
- Session cookie handling

## Usage Examples

### Basic Setup
The CORS middleware is automatically configured in `src/main.py`:

```python
from src.app.core.middleware.cors_middleware import CORSMiddleware

app = FastAPI()
app.add_middleware(CORSMiddleware)  # Should be first middleware
```

### Frontend Integration

#### JavaScript/React Example
```javascript
// Configure your frontend to include credentials
fetch('https://api.yourdomain.com/api/endpoint', {
  method: 'POST',
  credentials: 'include',  // Include cookies
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token,
    'X-CSRF-Token': csrfToken
  },
  body: JSON.stringify(data)
});
```

#### Axios Configuration
```javascript
// Global axios configuration
axios.defaults.withCredentials = true;
axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
```

## Production Deployment

### Required Configuration
1. **Explicit Origins**: Set `CORS_ALLOW_ORIGINS` to your frontend domain(s)
2. **HTTPS Only**: Ensure all origins use HTTPS
3. **Minimal Headers**: Only include necessary headers
4. **Credential Security**: Enable only if needed

### Example Production Configuration
```bash
CORS_ENABLED=true
CORS_ALLOW_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-CSRF-Token
CORS_ALLOW_CREDENTIALS=true
CORS_EXPOSE_HEADERS=X-Total-Count
CORS_MAX_AGE=3600
```

## Testing

### Unit Tests
Run CORS-specific tests:
```bash
pytest src/tests/test_cors_config.py -v
pytest src/tests/test_cors_middleware.py -v
```

### Manual Testing
Test CORS functionality with curl:

```bash
# Test preflight request
curl -X OPTIONS \
  -H "Origin: https://app.yourdomain.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  https://api.yourdomain.com/api/endpoint

# Test actual request
curl -X POST \
  -H "Origin: https://app.yourdomain.com" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer token" \
  https://api.yourdomain.com/api/endpoint
```

## Troubleshooting

### Common Issues

1. **CORS Error in Browser**
   - Check that frontend origin is in `CORS_ALLOW_ORIGINS`
   - Verify HTTPS usage in production
   - Ensure credentials configuration matches frontend

2. **Preflight Failures**
   - Check that requested method is in `CORS_ALLOW_METHODS`
   - Verify requested headers are in `CORS_ALLOW_HEADERS`
   - Ensure OPTIONS method is allowed

3. **Production Validation Errors**
   - Set explicit origins (no wildcards)
   - Use HTTPS origins only
   - Configure all required environment variables

### Debug Logging
Enable debug logging to troubleshoot CORS issues:

```python
import logging
logging.getLogger("src.app.core.middleware.cors_middleware").setLevel(logging.DEBUG)
```

## Security Considerations

1. **Never use wildcard origins in production**
2. **Always use HTTPS in production**
3. **Minimize allowed headers and methods**
4. **Regularly review and update allowed origins**
5. **Monitor CORS-related logs for suspicious activity**

## Best Practices

1. **Environment Separation**: Use different configurations for dev/prod
2. **Minimal Permissions**: Only allow necessary origins, methods, and headers
3. **Regular Audits**: Review CORS configuration regularly
4. **Monitoring**: Log and monitor CORS requests
5. **Documentation**: Keep frontend teams informed of CORS policies
