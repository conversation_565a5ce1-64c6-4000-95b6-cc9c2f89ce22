# Postman Collection Setup Guide

## 🎯 Quick Setup Steps

### 1. Prerequisites Check

Before importing the collection, ensure you have:

```bash
# Check if SecuriNest backend is running
curl http://localhost:8000/

# Check if Keycloak is running
curl http://localhost:8080/

# Check if <PERSON><PERSON> is running (for rate limiting)
redis-cli ping
```

### 2. Environment Configuration

Create a `.env` file in your SecuriNest backend directory with these settings:

```bash
# Environment
ENVIRONMENT=development

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/securinest_db
AUTO_SEED_DATABASE=true

# CSRF Protection
CSRF_ENABLED=true
CSRF_SECRET_KEY=your_32_character_csrf_secret_key_here
CSRF_TOKEN_HEADER=X-CSRF-Token
CSRF_COOKIE_NAME=csrf_token
CSRF_TOKEN_EXPIRE_MINUTES=60
CSRF_EXEMPT_PATHS=/auth/login,/auth/callback,/auth/logout,/docs,/openapi.json,/redoc

# CORS Configuration
CORS_ENABLED=true
CORS_ALLOW_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-CSRF-Token,Origin,Accept
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REDIS_URL=redis://localhost:6379
RATE_LIMIT_DEFAULT_LIMIT=100
RATE_LIMIT_WINDOW_SECONDS=60

# Keycloak Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_CLIENT_SECRET=your_keycloak_client_secret
KEYCLOAK_MASTER_REALM=master
BACKEND_URL=http://localhost:8000

# JWT Configuration
JWT_ALGORITHM=RS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_HOURS=24
JWT_CACHE_TTL=300

# Authentication
AUTH_STATE_SECRET=your_32_character_auth_state_secret_here
AUTH_NONCE_SECRET=your_32_character_auth_nonce_secret_here
AUTH_SESSION_COOKIE_NAME=session_token
AUTH_REFRESH_COOKIE_NAME=refresh_token
AUTH_COOKIE_SECURE=false
AUTH_COOKIE_SAMESITE=lax
AUTH_COOKIE_MAX_AGE_SECONDS=86400
```

### 3. Start Required Services

```bash
# Start Docker services (Keycloak, PostgreSQL, Redis)
cd docker
docker-compose up -d

# Start SecuriNest backend
cd ..
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. Import Postman Collection

1. Open Postman
2. Click **Import** → **Upload Files**
3. Select all 3 files:
   - `SecuriNest_API_Collection.postman_collection.json`
   - `SecuriNest_Development_Environment.postman_environment.json`
   - `SecuriNest_Production_Environment.postman_environment.json`
4. Select **SecuriNest Development Environment** from the environment dropdown

### 5. Test the Setup

Run these requests in order:

1. **Get CSRF Token** - Should return 200 with a token
2. **Initiate Login** - Should return 200 with authorization_url
3. **Get Keycloak Health** - Should return 200 (tests Keycloak connectivity)

## 🔧 Manual OAuth2 Flow Setup

Since OAuth2 requires browser interaction, follow these steps:

### Step 1: Get Authorization URL
Run the "Initiate Login" request with:
```json
{
  "tenant_id": "1",
  "redirect_uri": "http://localhost:3000/dashboard"
}
```

### Step 2: Complete Browser Authentication
1. Copy the `authorization_url` from the response
2. Open it in your browser
3. Login with Keycloak credentials (admin/admin for development)
4. You'll be redirected to a URL like:
   ```
   http://localhost:3000/dashboard?code=AUTH_CODE_HERE&state=STATE_HERE
   ```

### Step 3: Extract Authorization Code
1. Copy the `code` parameter from the URL
2. In Postman, go to the Development Environment
3. Set the `auth_code` variable to the copied code value

### Step 4: Complete Authentication
Run the "Handle OAuth Callback" request - this will set your access token automatically.

## 🧪 Testing Security Features

### CSRF Protection Test
1. Run "Get CSRF Token" first
2. Try "Test CSRF Protection" - should fail without token
3. Run any POST/PUT/PATCH/DELETE request - should work with token

### Rate Limiting Test
1. Run "Test Rate Limiting" multiple times quickly
2. Check response headers for rate limit information
3. Eventually should return 429 Too Many Requests

### CORS Test
1. Run "Test CORS Headers" (OPTIONS request)
2. Check for proper CORS headers in response

## 🚨 Common Issues & Solutions

### Issue: 401 Unauthorized
**Solution**: 
- Check if access token is set in environment
- Try refreshing the token with "Refresh Token" request
- Re-authenticate if refresh fails

### Issue: 403 CSRF Token Missing
**Solution**:
- Run "Get CSRF Token" request first
- Ensure CSRF_ENABLED=true in your .env file
- Check that X-CSRF-Token header is being sent

### Issue: CORS Errors
**Solution**:
- Verify CORS_ALLOW_ORIGINS includes your origin
- Check that Origin header is being sent
- Ensure CORS_ENABLED=true in your .env file

### Issue: Rate Limit Exceeded
**Solution**:
- Wait for the rate limit window to reset
- Check X-RateLimit-Reset header for reset time
- Reduce request frequency

### Issue: Keycloak Connection Failed
**Solution**:
- Ensure Keycloak is running on port 8080
- Check KEYCLOAK_URL in your .env file
- Verify Docker services are up: `docker-compose ps`

## 📊 Environment Variables Reference

| Variable | Development | Production | Description |
|----------|-------------|------------|-------------|
| `base_url` | http://localhost:8000 | https://api.yourdomain.com | API base URL |
| `keycloak_url` | http://localhost:8080 | https://auth.yourdomain.com | Keycloak URL |
| `frontend_origin` | http://localhost:3000 | https://app.yourdomain.com | Frontend URL |
| `tenant_id` | 1 | (set manually) | Default tenant ID |
| `access_token` | (auto-set) | (auto-set) | JWT access token |
| `csrf_token` | (auto-set) | (auto-set) | CSRF protection token |

## 🎉 Success Indicators

You'll know everything is working when:

✅ CSRF token requests return 200 with a token  
✅ Login initiation returns authorization URL  
✅ OAuth callback sets access token  
✅ Protected endpoints return data (not 401/403)  
✅ Rate limiting headers appear in responses  
✅ CORS preflight requests succeed  

## 📞 Need Help?

1. Check the main README.md for detailed API documentation
2. Review the .env.example file for configuration reference
3. Check backend logs for detailed error messages
4. Verify all services are running with `docker-compose ps`

Happy testing! 🚀
