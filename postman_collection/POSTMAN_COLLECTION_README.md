# SecuriNest API Postman Collection

This comprehensive Postman collection provides complete testing capabilities for the SecuriNest API, including all security features (CORS, CSRF, Rate Limiting) and Keycloak OAuth2 authentication.

## 📁 Files Included

1. **`SecuriNest_API_Collection.postman_collection.json`** - Main API collection
2. **`SecuriNest_Development_Environment.postman_environment.json`** - Development environment variables
3. **`SecuriNest_Production_Environment.postman_environment.json`** - Production environment variables
4. **`POSTMAN_COLLECTION_README.md`** - This documentation file

## 🚀 Quick Start

### 1. Import into Postman

1. Open Postman
2. Click **Import** button
3. Drag and drop all JSON files or click **Upload Files**
4. Select the appropriate environment (Development/Production)

### 2. Environment Setup

#### Development Environment
- **Base URL**: `http://localhost:8000`
- **Keycloak URL**: `http://localhost:8080`
- **Frontend Origin**: `http://localhost:3000`

#### Production Environment
- Update the URLs in the production environment to match your deployment
- Set your actual domain names and IP addresses

### 3. Prerequisites

Before using the collection, ensure:

1. **SecuriNest Backend** is running on the configured port
2. **Keycloak** is running and configured
3. **Database** is seeded with test data (for development)
4. **Redis** is running (for rate limiting)

## 🔐 Authentication Flow

### Step 1: Get CSRF Token (Required for state-changing operations)

```
GET /csrf/token
```

This automatically sets the `csrf_token` environment variable.

### Step 2: Initiate OAuth2 Login

```
POST /auth/login
{
  "tenant_id": "1",
  "redirect_uri": "http://localhost:3000/dashboard"
}
```

This returns an `authorization_url` that you need to visit in your browser.

### Step 3: Complete OAuth2 Flow

1. Copy the `authorization_url` from the login response
2. Open it in your browser
3. Complete the Keycloak authentication
4. Copy the `code` parameter from the callback URL
5. Set the `auth_code` environment variable in Postman
6. Run the "Handle OAuth Callback" request

### Step 4: Use Protected Endpoints

Once authenticated, the `access_token` is automatically set and used for all subsequent requests.

## 🛡️ Security Features Testing

### CORS (Cross-Origin Resource Sharing)

The collection automatically includes:
- `Origin` header for all requests
- Preflight OPTIONS requests for testing
- Validation of CORS response headers

### CSRF (Cross-Site Request Forgery) Protection

- Automatically fetches CSRF tokens
- Includes `X-CSRF-Token` header for POST/PUT/PATCH/DELETE requests
- Test endpoint to verify CSRF protection is active

### Rate Limiting

- Includes rate limiting headers in responses
- Test endpoint to verify rate limiting is working
- Monitors `X-RateLimit-*` headers

### JWT Token Management

- Automatic token storage and usage
- Token expiry tracking
- Refresh token functionality

## 📋 API Endpoints Covered

### 🔐 Authentication & Security
- CSRF token management
- OAuth2 authentication flow (login, callback, refresh, logout)

### 👥 User Management
- Create, read, update, delete users
- User role management

### 🏢 Tenant Management
- Tenant creation and management
- Multi-tenant isolation testing

### 🔑 API Key Management
- API key creation and management
- Key-based authentication testing

### 📁 Project Management
- Project CRUD operations
- Project-tenant relationships

### 💳 Billing & Subscriptions
- Plan management
- Subscription lifecycle
- Payment processing
- Usage tracking

### 🔧 Keycloak Management
- Health checks
- Realm management
- Tenant realm operations

### 🛡️ Security Testing
- Rate limiting verification
- CSRF protection testing
- CORS header validation

## 🔧 Environment Variables

### Automatically Set Variables
- `access_token` - JWT access token
- `csrf_token` - CSRF protection token
- `auth_state` - OAuth2 state parameter
- `user_id` - Current user ID
- `tenant_id` - Current tenant ID
- Various resource IDs (project_id, plan_id, etc.)

### Manually Set Variables
- `auth_code` - OAuth2 authorization code from Keycloak callback
- `base_url` - API base URL
- `keycloak_url` - Keycloak server URL
- `frontend_origin` - Frontend application URL

## 🧪 Testing Scenarios

### 1. Complete Authentication Flow
1. Get CSRF token
2. Initiate login
3. Complete OAuth2 flow
4. Test protected endpoints
5. Refresh token
6. Logout

### 2. Security Feature Validation
1. Test CORS preflight requests
2. Verify CSRF protection
3. Check rate limiting
4. Validate JWT token handling

### 3. API Functionality Testing
1. Create tenant and users
2. Generate API keys
3. Create projects and resources
4. Test billing and subscriptions
5. Monitor usage records

## 🚨 Important Notes

### Development vs Production

- **Development**: Uses HTTP, relaxed security settings
- **Production**: Uses HTTPS, strict security settings

### Rate Limiting

- Development: More permissive limits
- Production: Strict limits for security
- Monitor `X-RateLimit-*` headers to avoid hitting limits

### CSRF Protection

- Required for all state-changing operations (POST, PUT, PATCH, DELETE)
- Automatically handled by the collection's pre-request scripts
- Can be disabled in development for testing

### Token Management

- Access tokens expire after 30 minutes (configurable)
- Refresh tokens are stored in httpOnly cookies
- Automatic token refresh is implemented in the collection

## 🔍 Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check if access token is valid and not expired
2. **403 Forbidden**: Verify CSRF token is included for state-changing requests
3. **429 Too Many Requests**: Rate limit exceeded, wait before retrying
4. **CORS Errors**: Ensure Origin header matches allowed origins

### Debug Tips

1. Check the Postman Console for detailed request/response logs
2. Verify environment variables are set correctly
3. Ensure the backend server is running and accessible
4. Check Keycloak is properly configured and running

## 📞 Support

For issues with the API collection:
1. Check the SecuriNest backend logs
2. Verify environment configuration
3. Test individual endpoints to isolate issues
4. Review the authentication flow step by step

---

**Happy Testing! 🚀**
