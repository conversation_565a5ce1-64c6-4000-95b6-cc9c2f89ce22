{"id": "securinest-dev-env", "name": "SecuriNest Development Environment", "values": [{"key": "base_url", "value": "http://localhost:8000", "description": "Base URL for the SecuriNest API", "enabled": true}, {"key": "keycloak_url", "value": "http://localhost:8080", "description": "Keycloak server URL", "enabled": true}, {"key": "frontend_origin", "value": "http://localhost:3000", "description": "Frontend application origin for CORS", "enabled": true}, {"key": "client_ip", "value": "127.0.0.1", "description": "Client IP address for rate limiting tests", "enabled": true}, {"key": "tenant_id", "value": "1", "description": "Default tenant ID for testing (use seeded data)", "enabled": true}, {"key": "access_token", "value": "", "description": "JWT access token (automatically set by auth flow)", "enabled": true}, {"key": "csrf_token", "value": "", "description": "CSRF token (automatically set by CSRF endpoint)", "enabled": true}, {"key": "auth_state", "value": "", "description": "OAuth2 state parameter (automatically set)", "enabled": true}, {"key": "auth_code", "value": "", "description": "OAuth2 authorization code (manually set from Keycloak callback)", "enabled": true}, {"key": "realm_name", "value": "", "description": "Keycloak realm name (automatically set)", "enabled": true}, {"key": "authorization_url", "value": "", "description": "Keycloak authorization URL (automatically set)", "enabled": true}, {"key": "token_expiry", "value": "", "description": "Access token expiry timestamp (automatically set)", "enabled": true}, {"key": "user_id", "value": "", "description": "Current user ID (automatically set after login)", "enabled": true}, {"key": "username", "value": "", "description": "Current username (automatically set after login)", "enabled": true}, {"key": "project_id", "value": "", "description": "Project ID for testing (automatically set when creating projects)", "enabled": true}, {"key": "api_key_id", "value": "", "description": "API Key ID for testing (automatically set when creating API keys)", "enabled": true}, {"key": "api_key", "value": "", "description": "API Key value (automatically set when creating API keys)", "enabled": true}, {"key": "plan_id", "value": "", "description": "Plan ID for testing (automatically set when creating plans)", "enabled": true}, {"key": "subscription_id", "value": "", "description": "Subscription ID for testing (automatically set when creating subscriptions)", "enabled": true}], "_postman_variable_scope": "environment"}