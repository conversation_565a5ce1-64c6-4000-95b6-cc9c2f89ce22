{"info": {"name": "SecuriNest API Collection", "description": "Comprehensive API testing collection for SecuriNest backend with CORS, CSRF, Rate Limiting, and Keycloak authentication", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script for CSRF and authentication", "const csrfProtectedMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];", "const currentMethod = pm.request.method;", "", "// Add CORS headers for all requests", "pm.request.headers.add({", "    key: 'Origin',", "    value: pm.environment.get('frontend_origin') || 'http://localhost:3000'", "});", "", "// Handle CSRF protection for state-changing requests", "if (csrfProtectedMethods.includes(currentMethod)) {", "    const csrfToken = pm.environment.get('csrf_token');", "    if (csrfToken) {", "        pm.request.headers.add({", "            key: 'X-CSRF-Token',", "            value: csrfToken", "        });", "    }", "}", "", "// Add rate limiting headers", "pm.request.headers.add({", "    key: 'X-Forwarded-For',", "    value: pm.environment.get('client_ip') || '127.0.0.1'", "});", "", "// Auto-refresh JWT token if expired", "const accessToken = pm.environment.get('access_token');", "const tokenExpiry = pm.environment.get('token_expiry');", "const now = Math.floor(Date.now() / 1000);", "", "if (accessToken && tokenExpiry && now >= tokenExpiry) {", "    console.log('Access token expired, attempting refresh...');", "    // Token refresh will be handled by specific refresh request", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for response validation", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Check for rate limiting headers", "if (pm.response.headers.has('X-RateLimit-Limit')) {", "    pm.test('Rate limit headers present', function () {", "        pm.expect(pm.response.headers.get('X-RateLimit-Limit')).to.exist;", "        pm.expect(pm.response.headers.get('X-RateLimit-Remaining')).to.exist;", "        pm.expect(pm.response.headers.get('X-RateLimit-Reset')).to.exist;", "    });", "}", "", "// Check CORS headers", "pm.test('CORS headers present', function () {", "    const origin = pm.request.headers.get('Origin');", "    if (origin) {", "        pm.expect(pm.response.headers.get('Access-Control-Allow-Origin')).to.exist;", "    }", "});", "", "// Store tokens from auth responses", "if (pm.response.json && pm.response.json().access_token) {", "    const responseJson = pm.response.json();", "    pm.environment.set('access_token', responseJson.access_token);", "    if (responseJson.expires_in) {", "        const expiry = Math.floor(Date.now() / 1000) + responseJson.expires_in;", "        pm.environment.set('token_expiry', expiry);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "keycloak_url", "value": "http://localhost:8080", "type": "string"}, {"key": "frontend_origin", "value": "http://localhost:3000", "type": "string"}, {"key": "client_ip", "value": "127.0.0.1", "type": "string"}], "item": [{"name": "🔐 Authentication & Security", "item": [{"name": "🎫 CSRF Token Management", "item": [{"name": "Get CSRF Token", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/csrf/token", "host": ["{{base_url}}"], "path": ["csrf", "token"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('CSRF token retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.token).to.exist;", "    pm.environment.set('csrf_token', responseJson.token);", "});"]}}]}, {"name": "Get CSRF Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/csrf/info", "host": ["{{base_url}}"], "path": ["csrf", "info"]}}}, {"name": "Validate CSRF Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"csrf_token\": \"{{csrf_token}}\"\n}"}, "url": {"raw": "{{base_url}}/csrf/validate", "host": ["{{base_url}}"], "path": ["csrf", "validate"]}}}]}, {"name": "🔑 OAuth2 Authentication Flow", "item": [{"name": "1. Initiate <PERSON>gin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tenant_id\": \"{{tenant_id}}\",\n  \"redirect_uri\": \"{{frontend_origin}}/dashboard\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Login initiation successful', function () {", "    pm.response.to.have.status(200);", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.authorization_url).to.exist;", "    pm.expect(responseJson.state).to.exist;", "    pm.expect(responseJson.realm_name).to.exist;", "    pm.environment.set('auth_state', responseJson.state);", "    pm.environment.set('realm_name', responseJson.realm_name);", "    pm.environment.set('authorization_url', responseJson.authorization_url);", "});"]}}]}, {"name": "2. <PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/callback?code={{auth_code}}&state={{auth_state}}", "host": ["{{base_url}}"], "path": ["auth", "callback"], "query": [{"key": "code", "value": "{{auth_code}}", "description": "Authorization code from Keycloak"}, {"key": "state", "value": "{{auth_state}}", "description": "OAuth2 state parameter"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('OAuth callback successful', function () {", "    pm.response.to.have.status(200);", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.access_token).to.exist;", "    pm.expect(responseJson.user_id).to.exist;", "    pm.expect(responseJson.username).to.exist;", "    pm.environment.set('access_token', responseJson.access_token);", "    pm.environment.set('user_id', responseJson.user_id);", "    pm.environment.set('username', responseJson.username);", "    if (responseJson.expires_in) {", "        const expiry = Math.floor(Date.now() / 1000) + responseJson.expires_in;", "        pm.environment.set('token_expiry', expiry);", "    }", "});"]}}]}, {"name": "3. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Token refresh successful', function () {", "    pm.response.to.have.status(200);", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.access_token).to.exist;", "    pm.environment.set('access_token', responseJson.access_token);", "    if (responseJson.expires_in) {", "        const expiry = Math.floor(Date.now() / 1000) + responseJson.expires_in;", "        pm.environment.set('token_expiry', expiry);", "    }", "});"]}}]}, {"name": "4. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"redirect_uri\": \"{{frontend_origin}}/login\"\n}"}, "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Logout successful', function () {", "    pm.response.to.have.status(200);", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    // Clear stored tokens", "    pm.environment.unset('access_token');", "    pm.environment.unset('token_expiry');", "});"]}}]}]}]}, {"name": "👥 User Management", "item": [{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newuser\",\n  \"email\": \"<EMAIL>\",\n  \"first_name\": \"New\",\n  \"last_name\": \"User\",\n  \"password\": \"SecurePassword123!\",\n  \"roles\": [\"DEVELOPER\"]\n}"}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}}}, {"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"Updated\",\n  \"last_name\": \"User\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}}}]}, {"name": "🏢 Tenant Management", "item": [{"name": "Create Tenant", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"organization_name\": \"Test Organization\",\n  \"admin_username\": \"admin\",\n  \"admin_email\": \"<EMAIL>\",\n  \"admin_password\": \"SecurePassword123!\",\n  \"admin_first_name\": \"Admin\",\n  \"admin_last_name\": \"User\"\n}"}, "url": {"raw": "{{base_url}}/tenants", "host": ["{{base_url}}"], "path": ["tenants"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Tenant created successfully', function () {", "    pm.response.to.have.status(201);", "    const responseJson = pm.response.json();", "    if (responseJson.tenant_id) {", "        pm.environment.set('tenant_id', responseJson.tenant_id);", "    }", "});"]}}]}, {"name": "Get Tenant by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}"]}}}, {"name": "Get All Tenants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/tenants", "host": ["{{base_url}}"], "path": ["tenants"]}}}, {"name": "Update Tenant", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"organization_name\": \"Updated Organization Name\"\n}"}, "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}"]}}}, {"name": "Delete Tenant", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/tenants/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["tenants", "{{tenant_id}}"]}}}]}, {"name": "🔑 API Key Management", "item": [{"name": "Create API Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test API Key\",\n  \"description\": \"API key for testing purposes\",\n  \"expires_at\": \"2025-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api-keys", "host": ["{{base_url}}"], "path": ["api-keys"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('API key created successfully', function () {", "    pm.response.to.have.status(201);", "    const responseJson = pm.response.json();", "    if (responseJson.api_key_id) {", "        pm.environment.set('api_key_id', responseJson.api_key_id);", "    }", "    if (responseJson.key) {", "        pm.environment.set('api_key', responseJson.key);", "    }", "});"]}}]}, {"name": "Get API Key by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api-keys/{{api_key_id}}", "host": ["{{base_url}}"], "path": ["api-keys", "{{api_key_id}}"]}}}, {"name": "Get All API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api-keys", "host": ["{{base_url}}"], "path": ["api-keys"]}}}, {"name": "Update API Key", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated API Key Name\",\n  \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/api-keys/{{api_key_id}}", "host": ["{{base_url}}"], "path": ["api-keys", "{{api_key_id}}"]}}}, {"name": "Delete API Key", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api-keys/{{api_key_id}}", "host": ["{{base_url}}"], "path": ["api-keys", "{{api_key_id}}"]}}}]}, {"name": "📁 Project Management", "item": [{"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Project\",\n  \"description\": \"A test project for API testing\",\n  \"tenant_id\": \"{{tenant_id}}\"\n}"}, "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Project created successfully', function () {", "    pm.response.to.have.status(201);", "    const responseJson = pm.response.json();", "    if (responseJson.project_id) {", "        pm.environment.set('project_id', responseJson.project_id);", "    }", "});"]}}]}, {"name": "Get Project by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}}}, {"name": "Get All Projects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}}}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Project Name\",\n  \"description\": \"Updated project description\"\n}"}, "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}}}, {"name": "Delete Project", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}}}]}, {"name": "💳 Billing & Subscriptions", "item": [{"name": "📋 Plans", "item": [{"name": "Create Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Plan\",\n  \"description\": \"Premium subscription plan\",\n  \"price\": 29.99,\n  \"currency\": \"USD\",\n  \"billing_cycle\": \"monthly\",\n  \"features\": [\"feature1\", \"feature2\"]\n}"}, "url": {"raw": "{{base_url}}/plans", "host": ["{{base_url}}"], "path": ["plans"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Plan created successfully', function () {", "    pm.response.to.have.status(201);", "    const responseJson = pm.response.json();", "    if (responseJson.plan_id) {", "        pm.environment.set('plan_id', responseJson.plan_id);", "    }", "});"]}}]}, {"name": "Get All Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/plans", "host": ["{{base_url}}"], "path": ["plans"]}}}, {"name": "Get Plan by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/plans/{{plan_id}}", "host": ["{{base_url}}"], "path": ["plans", "{{plan_id}}"]}}}]}, {"name": "📊 Subscriptions", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": \"{{plan_id}}\",\n  \"tenant_id\": \"{{tenant_id}}\",\n  \"start_date\": \"2024-01-01T00:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/subscriptions", "host": ["{{base_url}}"], "path": ["subscriptions"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Subscription created successfully', function () {", "    pm.response.to.have.status(201);", "    const responseJson = pm.response.json();", "    if (responseJson.subscription_id) {", "        pm.environment.set('subscription_id', responseJson.subscription_id);", "    }", "});"]}}]}, {"name": "Get All Subscriptions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/subscriptions", "host": ["{{base_url}}"], "path": ["subscriptions"]}}}]}, {"name": "💰 Payments", "item": [{"name": "Create Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"subscription_id\": \"{{subscription_id}}\",\n  \"amount\": 29.99,\n  \"currency\": \"USD\",\n  \"payment_method\": \"credit_card\"\n}"}, "url": {"raw": "{{base_url}}/payments", "host": ["{{base_url}}"], "path": ["payments"]}}}, {"name": "Get All Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/payments", "host": ["{{base_url}}"], "path": ["payments"]}}}]}, {"name": "📈 Usage Records", "item": [{"name": "Create Usage Record", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"subscription_id\": \"{{subscription_id}}\",\n  \"usage_type\": \"api_calls\",\n  \"quantity\": 100,\n  \"timestamp\": \"2024-01-01T12:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/usage-records", "host": ["{{base_url}}"], "path": ["usage-records"]}}}, {"name": "Get All Usage Records", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/usage-records", "host": ["{{base_url}}"], "path": ["usage-records"]}}}]}]}, {"name": "🔧 Keycloak Management", "item": [{"name": "Get Keycloak Health", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/keycloak/health", "host": ["{{base_url}}"], "path": ["keycloak", "health"]}}}, {"name": "Get Tenant Realm Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/keycloak/tenant/{{tenant_id}}/info", "host": ["{{base_url}}"], "path": ["keycloak", "tenant", "{{tenant_id}}", "info"]}}}, {"name": "Delete Tenant Realm", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/keycloak/tenant/{{tenant_id}}", "host": ["{{base_url}}"], "path": ["keycloak", "tenant", "{{tenant_id}}"]}}}]}, {"name": "🛡️ Security Testing", "item": [{"name": "Test Rate Limiting", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tenant_id\": \"{{tenant_id}}\",\n  \"redirect_uri\": \"{{frontend_origin}}/dashboard\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Rate limiting headers present', function () {", "    if (pm.response.headers.has('X-RateLimit-Limit')) {", "        pm.expect(pm.response.headers.get('X-RateLimit-Limit')).to.exist;", "        pm.expect(pm.response.headers.get('X-RateLimit-Remaining')).to.exist;", "        pm.expect(pm.response.headers.get('X-RateLimit-Reset')).to.exist;", "        console.log('Rate Limit:', pm.response.headers.get('X-RateLimit-Limit'));", "        console.log('Remaining:', pm.response.headers.get('X-RateLimit-Remaining'));", "    }", "});"]}}]}, {"name": "Test CSRF Protection", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test without CSRF\",\n  \"description\": \"This should fail without CSRF token\"\n}"}, "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}}, "event": [{"listen": "prerequest", "script": {"exec": ["// Remove CSRF token to test protection", "pm.request.headers.remove('X-CSRF-Token');"]}}, {"listen": "test", "script": {"exec": ["pm.test('CSRF protection active', function () {", "    // Should return 403 if CSRF protection is enabled", "    pm.expect([403, 401]).to.include(pm.response.code);", "});"]}}]}, {"name": "Test CORS Headers", "request": {"method": "OPTIONS", "header": [{"key": "Origin", "value": "{{frontend_origin}}"}, {"key": "Access-Control-Request-Method", "value": "POST"}, {"key": "Access-Control-Request-Headers", "value": "Content-Type, Authorization"}], "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('CORS preflight successful', function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.headers.get('Access-Control-Allow-Origin')).to.exist;", "    pm.expect(pm.response.headers.get('Access-Control-Allow-Methods')).to.exist;", "    pm.expect(pm.response.headers.get('Access-Control-Allow-Headers')).to.exist;", "});"]}}]}]}]}