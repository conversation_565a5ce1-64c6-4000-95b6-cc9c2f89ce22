#!/usr/bin/env python3
"""
Integration test for CSRF protection with FastAPI.
"""

import os
import sys
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up minimal environment variables for testing
os.environ.update({
    'CSRF_SECRET_KEY': 'test_secret_key_that_is_long_enough_for_security_requirements_and_testing',
    'CSRF_ENABLED': 'true',
    'CSRF_TOKEN_HEADER': 'X-CSRF-Token',
    'CSRF_COOKIE_NAME': 'csrf_token',
    'CSRF_TOKEN_EXPIRE_MINUTES': '60',
    'CSRF_EXEMPT_PATHS': '/auth/login,/auth/callback,/docs',
    'ENVIRONMENT': 'testing',
    # Auth config (required by middleware)
    'AUTH_SESSION_COOKIE_NAME': 'session_token',
    'AUTH_REFRESH_COOKIE_NAME': 'refresh_token'
})

def test_fastapi_integration():
    """Test CSRF protection with FastAPI application."""
    print("Testing FastAPI integration...")

    # Force CSRF enabled for this test
    os.environ['CSRF_ENABLED'] = 'true'

    try:
        from fastapi import FastAPI, Request
        from fastapi.testclient import TestClient
        from src.app.core.config.csrf_config import csrf_config
        from src.app.core.middleware.csrf_middleware import CSRFMiddleware
        from src.app.core.security.csrf_service import csrf_service

        # Force enable CSRF for this test
        csrf_config.enabled = True
        
        # Create test FastAPI app
        app = FastAPI()
        app.add_middleware(CSRFMiddleware)
        
        # Add test endpoints
        @app.get("/test-get")
        async def test_get():
            return {"message": "GET request - should not require CSRF"}
        
        @app.post("/test-post")
        async def test_post(data: dict = None):
            return {"message": "POST request - requires CSRF", "data": data}
        
        @app.post("/auth/login")
        async def auth_login():
            return {"message": "Login endpoint - should be exempt"}
        
        client = TestClient(app)
        
        # Test 1: GET request should work without CSRF token
        response = client.get("/test-get")
        assert response.status_code == 200, f"GET request failed: {response.status_code}"
        assert "GET request" in response.json()["message"]
        
        # Test 2: POST request without CSRF token should fail
        response = client.post("/test-post", json={"data": "test"})
        assert response.status_code == 403, f"POST without CSRF should fail, got: {response.status_code}"
        assert "CSRF_TOKEN_INVALID" in response.json()["error"]
        
        # Test 3: Exempt path should work without CSRF token
        response = client.post("/auth/login", json={"username": "test"})
        assert response.status_code == 200, f"Exempt path should work, got: {response.status_code}"
        
        # Test 4: POST request with valid CSRF token should work
        token = csrf_service.generate_token()
        headers = {"X-CSRF-Token": token}
        response = client.post("/test-post", json={"data": "test"}, headers=headers)
        assert response.status_code == 200, f"POST with valid CSRF should work, got: {response.status_code}"
        
        # Test 5: POST request with invalid CSRF token should fail
        headers = {"X-CSRF-Token": "invalid_token"}
        response = client.post("/test-post", json={"data": "test"}, headers=headers)
        assert response.status_code == 403, f"POST with invalid CSRF should fail, got: {response.status_code}"
        
        print("✅ FastAPI integration test passed")

    except Exception as e:
        print(f"❌ FastAPI integration test failed: {e}")
        import traceback
        traceback.print_exc()
        raise  # Convert to assertion error for pytest
    finally:
        # Restore original setting
        os.environ['CSRF_ENABLED'] = 'false'

def test_csrf_router():
    """Test CSRF router endpoints."""
    print("Testing CSRF router...")

    # Force CSRF enabled for this test
    os.environ['CSRF_ENABLED'] = 'true'

    try:
        from fastapi import FastAPI
        from fastapi.testclient import TestClient
        from src.app.core.config.csrf_config import csrf_config
        from src.app.api.router.csrf_router import router

        # Force enable CSRF for this test
        csrf_config.enabled = True
        
        # Create test FastAPI app with CSRF router
        app = FastAPI()
        app.include_router(router)
        
        client = TestClient(app)
        
        # Test 1: Get CSRF token
        response = client.get("/csrf/token")
        assert response.status_code == 200, f"Token generation failed: {response.status_code}"
        
        data = response.json()
        assert "csrf_token" in data, "Response should contain csrf_token"
        assert "expires_in_minutes" in data, "Response should contain expiration info"
        assert data["expires_in_minutes"] == 60, "Token should expire in 60 minutes"
        
        # Test 2: Get CSRF info
        response = client.get("/csrf/info")
        assert response.status_code == 200, f"Info endpoint failed: {response.status_code}"
        
        info = response.json()
        assert "csrf_protection" in info, "Response should contain csrf_protection info"
        assert info["csrf_protection"]["enabled"] == True, "CSRF should be enabled"
        
        # Test 3: Validate CSRF token
        token = data["csrf_token"]
        response = client.post("/csrf/validate", json={"csrf_token": token})
        assert response.status_code == 200, f"Token validation failed: {response.status_code}"
        
        validation_result = response.json()
        assert validation_result["valid"] == True, "Token should be valid"
        
        print("✅ CSRF router test passed")

    except Exception as e:
        print(f"❌ CSRF router test failed: {e}")
        import traceback
        traceback.print_exc()
        raise  # Convert to assertion error for pytest
    finally:
        # Restore original setting
        os.environ['CSRF_ENABLED'] = 'false'

def test_json_body_token():
    """Test CSRF token in JSON body."""
    print("Testing JSON body token support...")

    # Force CSRF enabled for this test
    os.environ['CSRF_ENABLED'] = 'true'

    try:
        from fastapi import FastAPI
        from fastapi.testclient import TestClient
        from src.app.core.config.csrf_config import csrf_config
        from src.app.core.middleware.csrf_middleware import CSRFMiddleware
        from src.app.core.security.csrf_service import csrf_service

        # Force enable CSRF for this test
        csrf_config.enabled = True
        
        # Create test FastAPI app
        app = FastAPI()
        app.add_middleware(CSRFMiddleware)
        
        @app.post("/test-json-csrf")
        async def test_json_csrf(data: dict):
            return {"message": "Success", "received": data}
        
        client = TestClient(app)
        
        # Generate a valid token
        token = csrf_service.generate_token()
        
        # Test: POST request with CSRF token in JSON body
        payload = {
            "csrf_token": token,
            "user_data": "test_value"
        }
        
        response = client.post("/test-json-csrf", json=payload)
        assert response.status_code == 200, f"JSON body CSRF should work, got: {response.status_code}"
        
        result = response.json()
        assert "Success" in result["message"], "Request should succeed"
        
        print("✅ JSON body token test passed")

    except Exception as e:
        print(f"❌ JSON body token test failed: {e}")
        import traceback
        traceback.print_exc()
        raise  # Convert to assertion error for pytest
    finally:
        # Restore original setting
        os.environ['CSRF_ENABLED'] = 'false'

def main():
    """Run all integration tests."""
    print("🔒 Testing CSRF Protection Integration")
    print("=" * 50)
    
    tests = [
        test_fastapi_integration,
        test_csrf_router,
        test_json_body_token
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All CSRF integration tests passed!")
        return 0
    else:
        print("⚠️  Some integration tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
